<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Client;
use App\Models\Customer;
use App\Models\ClientPayment;
use App\Models\ClientPaymentHistory;
use App\Models\User;
use App\Models\Invoice;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\Request;
use App\Notifications\PaymentStatusUpdatedNotification;
use RealRashid\SweetAlert\Facades\Alert;


class ClientsController extends Controller
{

    // function __construct()
    // {
    //     $this->middleware('permission:Client.index', ['only' => ['index', 'show']]);
    //     $this->middleware('permission:Client.create', ['only' => ['create', 'store']]);
    //     $this->middleware('permission:Client.edit', ['only' => ['edit', 'update']]);
    //     $this->middleware('permission:Client.delete', ['only' => ['destroy']]);
    // }
    public function index(Request $request)
    {
        $search = $request->input('search');
        $clientsQuery = Client::query();
    if ($search) {
        $clientsQuery->where(function($query) use ($search) {
            $query->where('name', 'like', '%' . $search . '%')
            ->orWhere('national_number', 'like', '%' . $search . '%')
            ->orWhere('phone', 'like', '%' . $search . '%')
            ->orWhere('email', 'like', '%' . $search . '%')
            ->orWhere('additional_due_balance', 'like', '%' . $search . '%')
            ->paginate(5);
        });
    }

    $clients = $clientsQuery->paginate(25)->appends(['search' => $search]);

    if ($request->ajax()) {
        return view('clients._client-list', compact('clients'))->render();
    }
        return view('clients.index', compact('clients'));
    }

    public function create()
    {
        return view('clients.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'national_number' => 'required|string|max:255|unique:clients',
            'email' => 'nullable|string|email|max:255|unique:clients',
            'phone' => 'nullable|string|max:15',
        ]);

        $client = Client::create($request->all());

        $user = User::create([
            'name' => $client->name,
            'email' => $client->national_number,  // Using client's email for user email
            'password' => Hash::make($client->national_number),
            'role_name' => 'client',
        ]);

        $client->user_id = $user->id;
        $client->save();

        Alert::success('success', 'Client created successfully.');
        return redirect()->route('clients.index');
    }

//     public function show(Request $request, Client $client)
// {
//     // Load client wells data
//     $client->load('wells');

//     // Get all months from the invoices related to this client
//     $months = Invoice::whereHas('clientPayments', function ($query) use ($client) {
//         $query->where('client_id', $client->id);
//     })->pluck('month')->unique()->sortDesc();

//     // Get selected month from the request or default to the first available month
//     $selectedMonth = $request->query('month', $months->first());

//     // Get invoices for the selected month
//     $invoices = Invoice::where('month', $selectedMonth)
//         ->whereHas('clientPayments', function ($query) use ($client) {
//             $query->where('client_id', $client->id);
//         })
//         ->get();

//     // Calculate total hours worked and amount due based on wells and invoices
//     $totalHoursWorked = 0;
//     $amountDue = 0;
//     $displayedWells = [];

//     foreach ($invoices as $invoice) {
//         foreach ($invoice->wells as $well) {
//             // Calculate hours per well
//             $startTime = Carbon::parse($well->pivot->start_time);
//             $endTime = Carbon::parse($well->pivot->end_time);
//             $hours = $startTime->floatDiffInHours($endTime);
//             $totalHoursWorked += $hours;

//             // Add the well to the displayed wells
//             $displayedWells[$well->name]['price'] = $well->pivot->hour_price;
//             $displayedWells[$well->name]['hours'] = ($displayedWells[$well->name]['hours'] ?? 0) + $hours;

//             // Add the amount due for the well
//             $amountDue += $hours * $well->pivot->hour_price;
//         }
//     }

//     // Fetch payment status for the selected month
//     $paymentStatus = ClientPayment::where('client_id', $client->id)
//         ->where('invoice_month', $selectedMonth)
//         ->value('payment_status') ?: 'unpaid';

//     // Pass all necessary data to the view
//     return view('clients.invoice', compact('client', 'totalHoursWorked', 'amountDue', 'displayedWells', 'months', 'selectedMonth', 'paymentStatus'));
// }


public function show(Request $request, Client $client)
{
    // Load the client's wells and customers data
    $client->load('wells', 'customers.wells');

    // Define the available months by extracting the month based on the well's start time
    $months = Invoice::whereHas('clientPayments', function ($query) use ($client) {
        $query->where('client_id', $client->id);
    })->pluck('month')->map(function ($month) {
        return Carbon::parse($month)->format('Y-m-d');
    })->unique()->sortDesc();

    // Get the selected month from the request
    $selectedMonth = $request->query('month', $months->first());

    // Define date range for the selected month
    $startDate = Carbon::parse($selectedMonth)->subMonth()->day(16)->startOfDay();
    $endDate = Carbon::parse($selectedMonth)->day(15)->endOfDay();

    // Filter client hours within the date range (excluding customer hours and deleted records)
    $filteredClientHours = $client->hours()
        ->whereNull('deleted_at') // استثناء السجلات المحذوفة بـ soft deletes
        ->whereBetween('start_time', [$startDate, $endDate])
        ->with('well') // تحميل البئر المتعلقة
        ->get();

    // Filter customer hours within the date range (excluding deleted records)
    $filteredCustomerHours = collect();

    foreach ($client->customers as $customer) {
        $customerHours = $customer->hours()
            ->whereNull('deleted_at') // استثناء السجلات المحذوفة بـ soft deletes
            ->whereBetween('start_time', [$startDate, $endDate])
            ->with('well') // تحميل البئر المتعلقة
            ->get();

        foreach ($customerHours as $hour) {
            $filteredCustomerHours->push([
                'customer' => $customer,
                'hour' => $hour,
            ]);
        }
    }

    // Calculate total hours and amount due for client hours
    $clientHoursWorked = $filteredClientHours->sum(function ($hour) {
        $startTime = Carbon::parse($hour->start_time);
        $endTime = Carbon::parse($hour->end_time);
        return $startTime->floatDiffInHours($endTime);
    });

    $clientAmountDue = $filteredClientHours->sum(function ($hour) {
        $startTime = Carbon::parse($hour->start_time);
        $endTime = Carbon::parse($hour->end_time);
        $hours = $startTime->floatDiffInHours($endTime);
        return $hour->amount_due ?? ($hours * ($hour->hour_price ?? $hour->well->pivot->hour_price ?? 0));
    });

    // Calculate total hours and amount due for customer hours
    $customerHoursWorked = 0;
    $customerAmountDue = 0;
    $customerDetails = [];

    foreach ($filteredCustomerHours as $item) {
        $customer = $item['customer'];
        $hour = $item['hour'];
        $well = $hour->well;

        $startTime = Carbon::parse($hour->start_time);
        $endTime = Carbon::parse($hour->end_time);
        $hours = $startTime->floatDiffInHours($endTime);
        $amount = $hour->amount_due ?? ($hours * ($hour->hour_price ?? $well->pivot->hour_price ?? 0));

        $customerHoursWorked += $hours;
        $customerAmountDue += $amount;

        if (!isset($customerDetails[$customer->id])) {
            $customerDetails[$customer->id] = [
                'name' => $customer->name,
                'id' => $customer->id,
                'hours' => 0,
                'amount' => 0,
                'wells' => [], // تجميع الآبار مع ساعاتها
            ];
        }

        $customerDetails[$customer->id]['hours'] += $hours;
        $customerDetails[$customer->id]['amount'] += $amount;

        // تجميع البيانات حسب البئر
        $wellName = $well->name;
        $hourPrice = $hour->hour_price ?? $well->pivot->hour_price ?? 0;

        if (!isset($customerDetails[$customer->id]['wells'][$wellName])) {
            $customerDetails[$customer->id]['wells'][$wellName] = [
                'hours' => 0,
                'price' => $hourPrice,
            ];
        }

        $customerDetails[$customer->id]['wells'][$wellName]['hours'] += $hours;
    }

    // إجمالي الساعات بدون تكرار
    $totalHoursWorked = $clientHoursWorked + $customerHoursWorked;

    // Fetch payment details for the selected month
    $paymentDetails = ClientPayment::where('client_id', $client->id)
        ->where('invoice_month', $selectedMonth)
        ->first();
    $activeBonus = $paymentDetails->active_bonus ?? 0;
    $additionalAmount = $paymentDetails->additional_paid ?? 0;
    $totalAmountDue = $clientAmountDue + $customerAmountDue + $additionalAmount + $activeBonus;

    // Set default values for payment details
    $paymentStatus = $paymentDetails->payment_status ?? 'unpaid';
    $partialPayment = $paymentDetails->partial_payment ?? 0;
    $remainingAmount = $paymentDetails->remaining_amount ?? $totalAmountDue;

    // Group client wells by name and calculate hours and price per well
    $displayedClientWells = $filteredClientHours->groupBy(function ($hour) {
        return $hour->well->name;
    })->map(function ($hours) {
        $totalHours = $hours->sum(function ($hour) {
            $startTime = Carbon::parse($hour->start_time);
            $endTime = Carbon::parse($hour->end_time);
            return $startTime->floatDiffInHours($endTime);
        });
        $hourPrice = $hours->first()->hour_price ?? $hours->first()->well->pivot->hour_price ?? 0;
        return [
            'price' => $hourPrice,
            'hours' => $totalHours,
        ];
    });

    return view('clients.invoice', compact(
        'client', 'clientHoursWorked', 'clientAmountDue', 'totalHoursWorked', 'customerHoursWorked', 'customerAmountDue',
        'displayedClientWells', 'customerDetails', 'months', 'selectedMonth', 'paymentStatus',
        'partialPayment', 'remainingAmount', 'additionalAmount', 'totalAmountDue', 'activeBonus'
    ));
}



public function updatePaymentStatus(Request $request)
{
    // Validate the incoming request data
    $validated = $request->validate([
        'client_ids' => 'required|array',
        'client_ids.*' => 'exists:clients,id',
        'invoice_month' => 'required|date_format:Y-m-d',
        'payment_status' => 'required|in:unpaid,partial,paid',
        'partial_payment' => 'nullable|numeric|min:0',
        'payment_date' => 'required|date',
    ]);

    $clientIds = $validated['client_ids'];

    // Loop through each selected client/customer
    foreach ($clientIds as $clientSelection) {
        // Check if this is a customer selection (format: client_id_customer_id)
        if (strpos($clientSelection, '_') !== false) {
            list($clientId, $customerId) = explode('_', $clientSelection);

            // Find the corresponding customer payment record
            $clientPayment = ClientPayment::where('client_id', $clientId)
                ->where('customer_id', $customerId)
                ->whereMonth('invoice_month', Carbon::parse($validated['invoice_month'])->month)
                ->whereYear('invoice_month', Carbon::parse($validated['invoice_month'])->year)
                ->first();
        } else {
            // This is a main client selection
            $clientId = $clientSelection;
            $customerId = null;

            // Find the corresponding client payment record for the invoice month
            $clientPayment = ClientPayment::where('client_id', $clientId)
                ->whereNull('customer_id')
                ->whereMonth('invoice_month', Carbon::parse($validated['invoice_month'])->month)
                ->whereYear('invoice_month', Carbon::parse($validated['invoice_month'])->year)
                ->first();
        }

        if ($clientPayment) {
            if ($clientPayment->payment_status === 'paid' && $validated['payment_status'] === 'paid') {
                $client = Client::find($clientId);
                $customerName = $customerId ? Customer::find($customerId)->name : '';
                $entityName = $customerId ? "{$client->name} - {$customerName}" : $client->name;
                return redirect()->back()->with('warning', "فاتورة {$entityName} لشهر {$validated['invoice_month']} مدفوعة بالفعل.");
            }

            // Calculate amounts for this specific payment (main client or customer)
            $totalAmountDue = $clientPayment->amount_due + $clientPayment->additional_paid + $clientPayment->active_bonus;

            // Calculate partial payment and remaining amount
            $partialPayment = $validated['partial_payment'] ?? 0;
            $currentPartialPayment = $clientPayment->partial_payment ?? 0;
            $newPartialPayment = $currentPartialPayment + $partialPayment;

            $remainingAmount = $totalAmountDue - $newPartialPayment;

            // Handle the different payment statuses
            switch ($validated['payment_status']) {
                case 'paid':
                    // Mark all payments as paid
                    foreach ($allClientPayments as $payment) {
                        $payment->update([
                            'payment_status' => 'paid',
                            'partial_payment' => $totalAmountDue,
                            'remaining_amount' => 0,
                            'payment_date' => $validated['payment_date'],
                        ]);
                    }

                    // Log the payment history for main client payment only
                    $finalPaymentAmount = $totalAmountDue - $currentPartialPayment;
                    ClientPaymentHistory::create([
                        'client_payment_id' => $mainClientPayment->id,
                        'payment_amount' => $finalPaymentAmount,
                        'partial_payment' => $totalAmountDue,
                        'remaining_amount' => 0,
                        'payment_date' => $validated['payment_date'],
                    ]);
                    break;

                case 'unpaid':
                    // Mark all payments as unpaid
                    foreach ($allClientPayments as $payment) {
                        $payment->update([
                            'payment_status' => 'unpaid',
                            'partial_payment' => null,
                            'remaining_amount' => $totalAmountDue,
                            'payment_date' => $validated['payment_date'],
                        ]);
                    }
                    break;

                    case 'partial':
                        // Check if the partial payment completes the full amount
                        if ($remainingAmount <= 0) {
                            // Mark all as fully paid
                            foreach ($allClientPayments as $payment) {
                                $payment->update([
                                    'payment_status' => 'paid',
                                    'partial_payment' => $totalAmountDue,
                                    'remaining_amount' => 0,
                                    'payment_date' => $validated['payment_date'],
                                ]);
                            }

                            // Log the final payment for main client only
                            ClientPaymentHistory::create([
                                'client_payment_id' => $mainClientPayment->id,
                                'payment_amount' => $partialPayment,
                                'partial_payment' => $totalAmountDue,
                                'remaining_amount' => 0,
                                'payment_date' => $validated['payment_date'],
                            ]);
                        } else {
                            // بدلاً من التوزيع النسبي، نخصم المبلغ مباشرة من الدفعة الرئيسية
                            $mainClientPartial = ($mainClientPayment->partial_payment ?? 0) + $partialPayment;
                            $mainClientRemaining = $mainClientPayment->amount_due - $mainClientPartial;

                            $mainClientPayment->update([
                                'payment_status' => $mainClientRemaining <= 0 ? 'paid' : 'partial',
                                'partial_payment' => $mainClientPartial,
                                'remaining_amount' => max($remainingAmount, 0),
                                'payment_date' => $validated['payment_date'],
                            ]);

                            // تحديث الدفعات الأخرى (العملاء الشركاء) إذا لزم الأمر
                            foreach ($allClientPayments as $payment) {
                                if ($payment->id !== $mainClientPayment->id) {
                                    $payment->update([
                                        'payment_status' => 'partial',
                                        'partial_payment' => $payment->partial_payment ?? 0,
                                        'remaining_amount' => max($remainingAmount, 0),
                                        'payment_date' => $validated['payment_date'],
                                    ]);
                                }
                            }

                            // تسجيل الدفعة الجزئية
                            ClientPaymentHistory::create([
                                'client_payment_id' => $mainClientPayment->id,
                                'payment_amount' => $partialPayment,
                                'partial_payment' => $newPartialPayment,
                                'remaining_amount' => max($remainingAmount, 0),
                                'payment_date' => $validated['payment_date'],
                            ]);
                        }
                        break;
            }

            // Optionally notify the client about the payment status update
            $client = Client::find($clientId);
            $invoiceMonth = $clientPayment->invoice_month;  // Retrieve the month in 'YYYY-MM' format
            $client->notify(new PaymentStatusUpdatedNotification($clientPayment, $invoiceMonth, $validated['payment_status']));
        }
    }

    return redirect()->back()->with('success', 'Payment status updated successfully.');
}





    public function showClient(Request $request, $id)
    {
        // تحميل الكلاينت مع العلاقات
        $client = Client::with('customers')->findOrFail($id);

        // جلب الشهور المتاحة بناءً على الفواتير
        $months = ClientPayment::where('client_id', $client->id)
            ->pluck('invoice_month')
            ->map(function ($month) {
                return Carbon::parse($month)->format('Y-m-d');
            })
            ->unique()
            ->sortDesc();

        // تحديد الشهر المختار من الطلب، أو الشهر الحالي إذا لم يتم اختيار شهر
        $selectedMonth = $request->query('month', null);
        $today = Carbon::today(); // اليوم 23 مارس 2025

        if ($selectedMonth && $selectedMonth !== 'current') {
            $monthStart = Carbon::parse($selectedMonth)->subMonth()->day(16)->startOfDay();
            $monthEnd = Carbon::parse($selectedMonth)->day(15)->endOfDay();
            $currentMonth = Carbon::parse($selectedMonth)->locale('ar')->translatedFormat('F Y');
        } else {
            // الشهر الحالي بناءً على التاريخ
            if ($today->day >= 16) {
                $monthStart = $today->copy()->day(16)->startOfDay(); // 16 مارس 2025
                $monthEnd = $today->copy()->addMonth()->day(15)->endOfDay(); // 15 أبريل 2025
                $currentMonth = $today->copy()->addMonth()->locale('ar')->translatedFormat('F Y'); // أبريل 2025
            } else {
                $monthStart = $today->copy()->subMonth()->day(16)->startOfDay(); // 16 فبراير 2025
                $monthEnd = $today->copy()->day(15)->endOfDay(); // 15 مارس 2025
                $currentMonth = $today->locale('ar')->translatedFormat('F Y'); // مارس 2025
            }
        }

        // جمع بيانات الكلاينت والكاستمرز للشهر المحدد
        $currentMonthData = [
            'month' => $currentMonth,
            'client_hours' => 0,
            'customer_hours' => 0,
            'total_hours' => 0,
            'client_amount_due' => 0,
            'customer_amount_due' => 0,
            'additional_paid' => 0,
            'active_bonus' => 0,
            'remaining_amount' => 0,
            'payment_status' => 'unpaid',
            'wells' => [],
            'customers' => [],
        ];

        $customerData = [];

        // جمع الساعات مباشرة من جدول hours للكلاينت
        $clientHours = $client->hours()
            ->whereBetween('start_time', [$monthStart, $monthEnd])
            ->whereNull('customer_id') // الساعات الخاصة بالكلاينت فقط (بدون كاستمر)
            ->whereNull('deleted_at') // استثناء السجلات المحذوفة
            ->with('well')
            ->get();

        foreach ($clientHours as $hour) {
            $hours = Carbon::parse($hour->start_time)->floatDiffInHours(Carbon::parse($hour->end_time));
            $amount = $hour->amount_due ?? ($hours * ($hour->hour_price ?? $hour->well->pivot->hour_price ?? 0));

            $currentMonthData['client_hours'] += $hours;
            $currentMonthData['total_hours'] += $hours;
            $currentMonthData['client_amount_due'] += $amount;

            if (!isset($currentMonthData['wells'][$hour->well_id])) {
                $currentMonthData['wells'][$hour->well_id] = [
                    'name' => $hour->well->name ?? 'بئر غير معروف',
                    'hours' => 0,
                    'hour_price' => $hour->hour_price ?? $hour->well->pivot->hour_price ?? 0,
                ];
            }
            $currentMonthData['wells'][$hour->well_id]['hours'] += $hours;
        }

        // جمع الساعات للكاستمرز المرتبطين بالكلاينت
        $processedCustomerWellIds = collect();

        foreach ($client->customers as $customer) {
            $customerHours = $customer->hours()
                ->whereBetween('start_time', [$monthStart, $monthEnd])
                ->whereNull('deleted_at') // استثناء السجلات المحذوفة
                ->with('well')
                ->get();

            foreach ($customerHours as $hour) {
                $well = $hour->well;
                $uniqueKey = $customer->id . '_' . $well->id;

                if (!$processedCustomerWellIds->contains($uniqueKey)) {
                    $hours = Carbon::parse($hour->start_time)->floatDiffInHours(Carbon::parse($hour->end_time));
                    $amount = $hour->amount_due ?? ($hours * ($hour->hour_price ?? $well->pivot->hour_price ?? 0));

                    $currentMonthData['customer_hours'] += $hours;
                    $currentMonthData['total_hours'] += $hours;
                    $currentMonthData['customer_amount_due'] += $amount;

                    if (!isset($customerData[$customer->id])) {
                        $customerData[$customer->id] = [
                            'name' => $customer->name ?? 'كاستمر ' . $customer->id,
                            'hours' => 0,
                            'amount_due' => 0,
                            'wells' => [],
                        ];
                    }
                    $customerData[$customer->id]['hours'] += $hours;
                    $customerData[$customer->id]['amount_due'] += $amount;

                    if (!isset($customerData[$customer->id]['wells'][$well->id])) {
                        $customerData[$customer->id]['wells'][$well->id] = [
                            'name' => $well->name ?? 'بئر غير معروف',
                            'hours' => 0,
                            'hour_price' => $hour->hour_price ?? $well->pivot->hour_price ?? 0,
                        ];
                    }
                    $customerData[$customer->id]['wells'][$well->id]['hours'] += $hours;

                    // إضافة الكاستمر إلى بيانات الشهر
                    $currentMonthData['customers'][$customer->id] = $customerData[$customer->id];

                    $processedCustomerWellIds->push($uniqueKey);
                }
            }
        }

        // جمع المبالغ الإضافية ورسوم الصيانة من جدول ClientPayment
        $additionalAndBonus = ClientPayment::where('client_id', $client->id)
            ->whereBetween('invoice_month', [$monthStart->toDateString(), $monthEnd->toDateString()])
            ->first();

        if ($additionalAndBonus) {
            $currentMonthData['additional_paid'] = $additionalAndBonus->additional_paid ?? 0;
            $currentMonthData['active_bonus'] = $additionalAndBonus->active_bonus ?? 0;
            $currentMonthData['payment_status'] = $additionalAndBonus->payment_status ?? 'unpaid';
        }

        // حساب المبلغ المتبقي
        $currentMonthData['remaining_amount'] = $currentMonthData['client_amount_due'] +
                                               $currentMonthData['customer_amount_due'] +
                                               $currentMonthData['additional_paid'] +
                                               $currentMonthData['active_bonus'];

        // تصحيح عدد الفواتير
        $totalInvoices = ClientPayment::where('client_id', $client->id)->count();

        return view('clients.ClientShow', compact('client', 'currentMonthData', 'customerData', 'totalInvoices', 'months', 'selectedMonth'));
    }







public function getAmountDue(Request $request)
{
    $selectedMonth = $request->input('invoice_month');
    $clientIds = $request->input('client_ids');

    if (!$selectedMonth || !$clientIds) {
        return response()->json([
            'amount_due' => 0,
            'remaining_amount' => 0,
            'client_statuses' => []
        ]);
    }

    $totalAmountDue = 0;
    $totalRemainingAmount = 0;
    $clientStatuses = [];

    // Loop through each client to get their payment status for the selected month
    foreach ($clientIds as $clientId) {
        $client = Client::find($clientId);
        if (!$client) continue;

        // Get client payment for the specific month
        $clientPayment = ClientPayment::where('client_id', $clientId)
            ->whereMonth('invoice_month', Carbon::parse($selectedMonth)->month)
            ->whereYear('invoice_month', Carbon::parse($selectedMonth)->year)
            ->first();

        if ($clientPayment) {
            // Get all payments for this client in this month (main client + partner customers)
            $allClientPayments = ClientPayment::where('client_id', $clientId)
                ->whereMonth('invoice_month', Carbon::parse($selectedMonth)->month)
                ->whereYear('invoice_month', Carbon::parse($selectedMonth)->year)
                ->get();

            $totalClientAmount = 0;
            $totalClientRemaining = 0;
            $additionalPaid = 0;
            $activeBonus = 0;

            foreach ($allClientPayments as $payment) {
                $totalClientAmount += $payment->amount_due;

                // Get additional amounts and maintenance fees from main client payment only
                if (is_null($payment->customer_id)) {
                    $additionalPaid = $payment->additional_paid ?? 0;
                    $activeBonus = $payment->active_bonus ?? 0;
                }

                // Sum up remaining amounts from all payments (main + partners)
                // Use remaining_amount if available, otherwise use amount_due
                if ($payment->remaining_amount !== null) {
                    $totalClientRemaining += $payment->remaining_amount;
                } else {
                    // If remaining_amount is null, assume full amount is due
                    $totalClientRemaining += $payment->amount_due;
                }
            }

            // Calculate full amount including maintenance fees
            $fullAmount = $totalClientAmount + $additionalPaid + $activeBonus;

            // Calculate remaining amount - maintenance fees are already included in remaining_amount
            // Only add additional_paid if it's not already included
            $remainingForThisClient = $totalClientRemaining;

            $totalAmountDue += $fullAmount;
            $totalRemainingAmount += max($remainingForThisClient, 0);

            // Add client status info
            $clientStatuses[] = [
                'id' => $clientId,
                'name' => $client->name,
                'payment_status' => $clientPayment->payment_status,
                'remaining_amount' => max($remainingForThisClient, 0),
                'total_amount' => $fullAmount,
                'has_invoice' => true
            ];
        } else {
            // Client has no invoice for this month
            $clientStatuses[] = [
                'id' => $clientId,
                'name' => $client->name,
                'payment_status' => 'no_invoice',
                'remaining_amount' => 0,
                'total_amount' => 0,
                'has_invoice' => false
            ];
        }
    }

    return response()->json([
        'amount_due' => $totalAmountDue,
        'remaining_amount' => $totalRemainingAmount,
        'client_statuses' => $clientStatuses
    ]);
}






public function showPaymentStatusForm(Request $request)
{
    $selectedMonth = $request->input('invoice_month');

    // If no month is selected, get all clients with any invoices
    if ($selectedMonth) {
        // Fetch all clients who have invoices in the selected month (regardless of payment status)
        $clients = Client::whereHas('clientPayments', function ($query) use ($selectedMonth) {
            $query->whereMonth('invoice_month', Carbon::parse($selectedMonth)->month)
                  ->whereYear('invoice_month', Carbon::parse($selectedMonth)->year);
        })->get();
    } else {
        // Get all clients who have any invoices
        $clients = Client::whereHas('clientPayments')->get();
    }

    $months = ClientPayment::select('invoice_month')
        ->distinct()
        ->orderBy('invoice_month', 'desc')
        ->pluck('invoice_month');

    return view('clients.payment', compact('clients', 'months'));
}


public function getUnpaidClients(Request $request)
{
    $selectedMonth = $request->input('invoice_month');

    // Get clients with unpaid status for the selected month
    $clients = Client::whereHas('clientPayments', function ($query) use ($selectedMonth) {
        $query->where('payment_status', 'unpaid')
              ->whereMonth('invoice_month', Carbon::parse($selectedMonth)->month)
              ->whereYear('invoice_month', Carbon::parse($selectedMonth)->year);
    })->get();

    return response()->json(['clients' => $clients]);
}


public function showPartialPayments($clientId)
{
    // Retrieve the client information
    $client = Client::findOrFail($clientId);

    // Get all partial payments along with related client payments
    $partialPayments = ClientPaymentHistory::whereHas('clientPayment', function($query) use ($clientId) {
        $query->where('client_id', $clientId);
    })->with(['clientPayment' => function($query) {
        // Ensure we get the invoice month and all payment fields including maintenance fees
        $query->select('id', 'client_id', 'invoice_id', 'amount_due', 'additional_paid', 'active_bonus', 'payment_status', 'partial_payment', 'remaining_amount')
              ->with('invoice');
    }])->get();
    foreach ($partialPayments as $payment) {
        // Assuming 'month' field is in the 'invoice' related model
        $payment->formattedMonth = \Carbon\Carbon::parse($payment->clientPayment->invoice->month)->format('F Y');

        // Handle Arabic month translation if the locale is 'ae'
        if (app()->getLocale() == 'ae') {
            $payment->formattedMonth = \Carbon\Carbon::parse($payment->clientPayment->invoice->month)
                                    ->locale('ar')
                                    ->translatedFormat('F Y');
        }
    }

    // Return the view with client and partial payment data
    return view('partial-payments.index', compact('client', 'partialPayments'));
}






    public function edit(Client $client)
    {
        return view('clients.edit', compact('client'));
    }

    public function update(Request $request, Client $client)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'national_number' => 'required|string|max:255|unique:clients,national_number,' . $client->id,
            'email' => 'required|string|email|max:255|unique:clients,email,' . $client->id,
            'phone' => 'required|string|max:15',
        ]);

        $client->update($request->all());

        $user = User::where('client_id', $client->id)->first();
        if ($user) {
            $user->update([
                'name' => $client->name,
                'email' => $client->national_number,
                'password' => Hash::make($client->national_number),
            ]);
        }
        Alert::success('success', 'Client updated successfully.');
        return redirect()->route('clients.index');
    }

    public function destroy(Client $client)
    {
        $user = User::where('client_id', $client->id)->first();
        if ($user) {
            $user->delete();
        }
        $client->delete();
        Alert::success('success', 'Client deleted successfully.');
        return redirect()->route('clients.index');
    }


    public function wellSummary(Request $request)
{
    // Get the current date and the start of the current month
    $currentDate = \Carbon\Carbon::now();
    $startOfMonth = $currentDate->copy()->startOfMonth();

    // If no date range is provided, set the default date range to the current month up to the current day
    if (!$request->has('date_range') || empty($request->input('date_range'))) {
        $request->merge([
            'date_range' => $startOfMonth->format('d M, Y') . ' to ' . $currentDate->format('d M, Y')
        ]);
    }

    // Parse date range for filtering
    $startDate = null;
    $endDate = null;
    if ($request->has('date_range') && !empty($request->input('date_range'))) {
        $dateRange = $request->input('date_range');

        // Function to parse date with Arabic month names
        $parseDate = function($dateStr) use ($startOfMonth, $currentDate) {
            // Try English format first
            try {
                return \Carbon\Carbon::createFromFormat('d M, Y', trim($dateStr));
            } catch (\Exception $e) {
                // Try with Arabic month names
                try {
                    // Map of Arabic month names to English
                    $arabicToEnglish = [
                        'يناير' => 'January',
                        'فبراير' => 'February',
                        'مارس' => 'March',
                        'أبريل' => 'April',
                        'مايو' => 'May',
                        'يونيو' => 'June',
                        'يوليو' => 'July',
                        'أغسطس' => 'August',
                        'سبتمبر' => 'September',
                        'أكتوبر' => 'October',
                        'نوفمبر' => 'November',
                        'ديسمبر' => 'December'
                    ];

                    // Extract day, month, year
                    preg_match('/(\d+)\s+([^\d,]+),\s+(\d+)/', trim($dateStr), $matches);

                    if (count($matches) >= 4) {
                        $day = $matches[1];
                        $arabicMonth = $matches[2];
                        $year = $matches[3];

                        // Convert Arabic month to English
                        $englishMonth = isset($arabicToEnglish[$arabicMonth]) ? $arabicToEnglish[$arabicMonth] : $arabicMonth;

                        // Create date with English month
                        return \Carbon\Carbon::parse("$day $englishMonth $year");
                    }

                    throw new \Exception("Could not parse date: $dateStr");
                } catch (\Exception $e) {
                    // If all parsing fails, return default
                    // Just return null without logging
                    return null;
                }
            }
        };

        if (strpos($dateRange, ' to ') !== false) {
            $dates = explode(' to ', $dateRange);
            $parsedStartDate = $parseDate(trim($dates[0]));

            if ($parsedStartDate) {
                $startDate = $parsedStartDate->startOfDay();

                if (count($dates) > 1) {
                    $parsedEndDate = $parseDate(trim($dates[1]));
                    $endDate = $parsedEndDate ? $parsedEndDate->endOfDay() : $startDate->copy()->endOfDay();
                } else {
                    $endDate = $startDate->copy()->endOfDay();
                }
            } else {
                // Use default dates if parsing fails
                $startDate = $startOfMonth;
                $endDate = $currentDate->copy()->endOfDay();
            }
        } else {
            $parsedDate = $parseDate(trim($dateRange));

            if ($parsedDate) {
                $startDate = $parsedDate->startOfDay();
                $endDate = $parsedDate->copy()->endOfDay();
            } else {
                // Use default dates if parsing fails
                $startDate = $startOfMonth;
                $endDate = $currentDate->copy()->endOfDay();
            }
        }
    }

    // Get clients with their wells (direct hours)
    $clients = Client::with(['wells' => function($query) use ($startDate, $endDate) {
        // Apply date range filter to wells
        if ($startDate && $endDate) {
            $query->whereBetween('hours.start_time', [$startDate, $endDate]);
        }
    }]);

    // Also load customers and their wells with date filter
    $clients = $clients->with(['customers' => function($query) use ($startDate, $endDate) {
        $query->with(['wells' => function($query) use ($startDate, $endDate) {
            $query->withPivot('start_time', 'end_time', 'hour_price', 'amount_due');
            // Apply date range filter to customer wells
            if ($startDate && $endDate) {
                $query->whereBetween('hours.start_time', [$startDate, $endDate]);
            }
        }]);
    }]);

    // Apply search filter
    if ($request->filled('search')) {
        $searchTerm = $request->input('search');
        $clients->where(function($query) use ($searchTerm) {
            $query->where('name', 'LIKE', "%{$searchTerm}%")
                  ->orWhereHas('wells', function ($query) use ($searchTerm) {
                      $query->where('name', 'LIKE', "%{$searchTerm}%");
                  })
                  ->orWhereHas('customers', function ($query) use ($searchTerm) {
                      $query->where('name', 'LIKE', "%{$searchTerm}%");
                  });
        });
    }

    // Get additional data for each client
    $clients = $clients->get();

    // Calculate total amount due for all clients based on the filtered date range
    $totalAmountDue = 0;
    $totalOperationalCosts = 0;

    foreach ($clients as $client) {
        // Calculate client's direct amount due
        $clientAmountDue = 0;
        foreach ($client->wells as $well) {
            $startTime = \Carbon\Carbon::parse($well->pivot->start_time);
            $endTime = \Carbon\Carbon::parse($well->pivot->end_time);

            // Skip if outside date range
            if ($startDate && $endTime < $startDate) continue;
            if ($endDate && $startTime > $endDate) continue;

            $hours = $startTime->floatDiffInHours($endTime);
            $clientAmountDue += $hours * $well->pivot->hour_price;
        }
        $client->direct_amount_due = $clientAmountDue;

        // Calculate customers' amount due
        $customersAmountDue = 0;
        foreach ($client->customers as $customer) {
            $customerAmountDue = 0;
            foreach ($customer->wells as $well) {
                $startTime = \Carbon\Carbon::parse($well->pivot->start_time);
                $endTime = \Carbon\Carbon::parse($well->pivot->end_time);

                // Skip if outside date range
                if ($startDate && $endTime < $startDate) continue;
                if ($endDate && $startTime > $endDate) continue;

                $hours = $startTime->floatDiffInHours($endTime);
                $customerAmountDue += $hours * $well->pivot->hour_price;
            }
            $customer->amount_due = $customerAmountDue;
            $customersAmountDue += $customerAmountDue;
        }
        $client->customers_amount_due = $customersAmountDue;

        // Add operational costs or remaining balance
        $operationalCosts = 0;
        if (isset($client->operational_costs)) {
            // If we have a date for the operational costs, check if it's within the filter range
            if (isset($client->operational_costs_date)) {
                $operationalCostsDate = \Carbon\Carbon::parse($client->operational_costs_date);
                if ((!$startDate || $operationalCostsDate >= $startDate) &&
                    (!$endDate || $operationalCostsDate <= $endDate)) {
                    $operationalCosts = $client->operational_costs;
                }
            } else {
                // If no date is specified, include it always
                $operationalCosts = $client->operational_costs;
            }
        }
        $client->operational_costs_due = $operationalCosts;

        // Calculate total amount due for this client (without operational costs)
        $client->total_amount_due = $client->direct_amount_due + $client->customers_amount_due;

        // Calculate total overall amount (including operational costs)
        $client->total_overall_amount = $client->total_amount_due + $client->operational_costs_due;

        // Add to totals
        $totalAmountDue += $client->direct_amount_due + $client->customers_amount_due;
        $totalOperationalCosts += $client->operational_costs_due;
    }

    // Total overall amount due
    $totalOverallAmountDue = $totalAmountDue + $totalOperationalCosts;

    if ($request->ajax()) {
        return view('clients.client_list', compact('clients'))->render();
    }

    return view('clients.well_summary', compact('clients', 'request', 'totalAmountDue', 'totalOperationalCosts', 'totalOverallAmountDue', 'startDate', 'endDate'));
}






}

