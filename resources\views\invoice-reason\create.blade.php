@extends('layouts.master')

@section('title')
    إنشاء فاتورة بأسباب
@endsection

@section('content')
    @component('components.breadcrumb')
        @slot('li_1') النماذج @endslot
        @slot('title') إنشاء فاتورة بأسباب @endslot
    @endcomponent

    @if(session('success'))
        <div class="alert alert-success">{{ session('success') }}</div>
    @elseif(session('error'))
        <div class="alert alert-danger">{{ session('error') }}</div>
    @endif

    @component('components.form', ['title' => 'إنشاء فاتورة بأسباب', 'action' => route('invoice-reason.store'), 'buttonText' => 'إنشاء الفاتورة'])
        @csrf
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="total_amount_due" class="form-label">إجمالي المبلغ المستحق</label>
                    <input type="number" name="total_amount_due" class="form-control @error('total_amount_due') is-invalid @enderror"
                           placeholder="أدخل إجمالي المبلغ المستحق" id="total_amount_due" required>
                    @error('total_amount_due')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <div class="col-md-6">
                <div class="mb-3">
                    <label for="reason" class="form-label">سبب الفاتورة</label>
                    <textarea name="reason" class="form-control @error('reason') is-invalid @enderror"
                              placeholder="أدخل سبب الفاتورة" id="reason" required></textarea>
                    @error('reason')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <div class="col-md-6">
                <div class="mb-3">
                    <label for="start_date" class="form-label">تاريخ البداية</label>
                    <input type="date" name="start_date" class="form-control @error('start_date') is-invalid @enderror"
                           id="start_date" required>
                    @error('start_date')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <div class="col-md-6">
                <div class="mb-3">
                    <label for="end_date" class="form-label">تاريخ النهاية</label>
                    <input type="date" name="end_date" class="form-control @error('end_date') is-invalid @enderror"
                           id="end_date" required>
                    @error('end_date')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>

        <!-- زر جلب بيانات الآبار -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <button type="button" id="fetch-wells-data" class="btn btn-info" onclick="window.fetchWellsData()">
                    <i class="mdi mdi-magnify"></i> جلب بيانات الآبار للفترة المحددة
                </button>
            </div>
        </div>

        <!-- قسم توزيع النسب على الآبار -->
        <div id="wells-distribution" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">توزيع النسب على الآبار</h5>
                </div>
                <div class="card-body">
                    <div id="wells-list">
                        <!-- سيتم ملء هذا القسم بـ JavaScript -->
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <strong>ملاحظة:</strong> مجموع النسب يجب أن يساوي 100%
                                <br><span id="total-percentage">المجموع الحالي: 0%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endcomponent
@endsection

@section('script')
    <script src="{{ URL::asset('/assets/libs/apexcharts/apexcharts.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/js/pages/dashboard-client-statistics-projects.init.js') }}"></script>
    <script src="{{ URL::asset('/assets/js/app.min.js') }}"></script>

    <script>
        // جعل الـ function في الـ global scope
        window.fetchWellsData = function() {
            const startDate = document.getElementById('start_date').value;
            const endDate = document.getElementById('end_date').value;
            const button = document.getElementById('fetch-wells-data');

            console.log('Fetching wells data for:', startDate, 'to', endDate);

            if (!startDate || !endDate) {
                alert('يرجى تحديد تاريخ البداية والنهاية أولاً');
                return;
            }

            // إظهار مؤشر التحميل
            button.innerHTML = '<i class="mdi mdi-loading mdi-spin"></i> جاري جلب البيانات...';
            button.disabled = true;

            // إرسال طلب AJAX
            fetch('{{ route("invoice-reason.get-wells-data") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    start_date: startDate,
                    end_date: endDate
                })
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    displayWellsData(data.wells);
                    document.getElementById('wells-distribution').style.display = 'block';
                } else {
                    alert('حدث خطأ: ' + (data.message || 'خطأ غير معروف'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في جلب البيانات: ' + error.message);
            })
            .finally(() => {
                // إعادة تعيين الزر
                button.innerHTML = '<i class="mdi mdi-magnify"></i> جلب بيانات الآبار للفترة المحددة';
                button.disabled = false;
            });
        }

        // إضافة event listener كمان
        document.addEventListener('DOMContentLoaded', function() {
            const button = document.getElementById('fetch-wells-data');
            if (button) {
                button.addEventListener('click', window.fetchWellsData);
            }
        });

        function displayWellsData(wells) {
            const wellsList = document.getElementById('wells-list');
            wellsList.innerHTML = '';

            wells.forEach((well, index) => {
                const wellDiv = document.createElement('div');
                wellDiv.className = 'row mb-3 well-item';
                wellDiv.innerHTML = `
                    <div class="col-md-4">
                        <label class="form-label"><strong>${well.name}</strong></label>
                        <input type="hidden" name="wells[${index}][id]" value="${well.id}">
                        <input type="hidden" name="wells[${index}][name]" value="${well.name}">
                        <input type="hidden" name="wells[${index}][total_hours]" value="${well.total_hours}">
                        <div class="text-muted small">إجمالي الساعات: ${well.total_hours.toFixed(2)} ساعة</div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">النسبة المئوية (%)</label>
                        <input type="number" name="wells[${index}][percentage]" class="form-control percentage-input"
                               min="0" max="100" step="0.01" placeholder="أدخل النسبة" required>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">المبلغ المخصص</label>
                        <input type="text" class="form-control allocated-amount" readonly>
                        <input type="hidden" name="wells[${index}][allocated_amount]" class="allocated-amount-hidden">
                    </div>
                `;
                wellsList.appendChild(wellDiv);
            });

            // إضافة مستمعي الأحداث للنسب
            addPercentageListeners();
        }

        function addPercentageListeners() {
            const percentageInputs = document.querySelectorAll('.percentage-input');
            const totalAmountInput = document.getElementById('total_amount_due');

            percentageInputs.forEach(input => {
                input.addEventListener('input', updateCalculations);
            });

            totalAmountInput.addEventListener('input', updateCalculations);
        }

        function updateCalculations() {
            const totalAmount = parseFloat(document.getElementById('total_amount_due').value) || 0;
            const percentageInputs = document.querySelectorAll('.percentage-input');
            let totalPercentage = 0;

            percentageInputs.forEach((input, index) => {
                const percentage = parseFloat(input.value) || 0;
                totalPercentage += percentage;

                // حساب المبلغ المخصص
                const allocatedAmount = (totalAmount * percentage) / 100;
                const allocatedAmountElement = document.querySelectorAll('.allocated-amount')[index];
                const allocatedAmountHidden = document.querySelectorAll('.allocated-amount-hidden')[index];

                allocatedAmountElement.value = allocatedAmount.toFixed(2) + ' جنيه';
                allocatedAmountHidden.value = allocatedAmount.toFixed(2);
            });

            // تحديث مجموع النسب
            const totalPercentageElement = document.getElementById('total-percentage');
            totalPercentageElement.textContent = `المجموع الحالي: ${totalPercentage.toFixed(2)}%`;

            // تغيير لون التحذير
            if (totalPercentage === 100) {
                totalPercentageElement.parentElement.className = 'alert alert-success';
            } else {
                totalPercentageElement.parentElement.className = 'alert alert-warning';
            }
        }
    </script>
@endsection
