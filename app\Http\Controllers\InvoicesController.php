<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Well;
use App\Models\Client;
use App\Models\ClientPayment;
use App\Models\ClientPaymentHistory;
use App\Models\ClientReasonBalance;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use App\Notifications\InvoiceCreatedNotification;
use Notification;
use RealRashid\SweetAlert\Facades\Alert;

class InvoicesController extends Controller
{
    public function index(Request $request)
{
    $search = $request->input('search');
    $invoicesQuery = Invoice::with(['wells' => function ($query) {
        $query->select('wells.id', 'name', 'hour_price');
    }]);

    if ($search) {
        $invoicesQuery->where(function($query) use ($search) {
            $query->where('total_amount_due', 'like', '%' . $search . '%')
            ->orWhere('second_additional_due_amount', 'like', '%' . $search . '%')
                  ->orWhere('month', 'like', '%' . $search . '%')
                  ->orWhereHas('wells', function($query) use ($search) {
                      $query->where('name', 'like', '%' . $search . '%');
                  });
        });
    }

    $invoices = $invoicesQuery->paginate(2)->appends(['search' => $search]);

    if ($request->ajax()) {
        return view('invoices.invoice-list', compact('invoices'))->render();
    }

    return view('invoices.index', compact('invoices'));
}



    public function create()
    {
        $wells = Well::all();
        return view('invoices.create', compact('wells'));
    }
    // public function store(Request $request)
    // {
    //     $request->validate([
    //         'total_amount_due' => 'required|numeric',
    //         'month' => 'required|date_format:Y-m',
    //         'well_ids' => 'required|array',
    //         'well_ids.*' => 'exists:wells,id',
    //         'hourly_rates' => 'nullable|array',
    //         'hourly_rates.*' => 'nullable|numeric',
    //     ]);

    //     $invoice = new Invoice();
    //     $invoice->total_amount_due = $request->total_amount_due;
    //     $invoice->month = Carbon::createFromFormat('Y-m', $request->month)->startOfMonth();
    //     $invoice->save();

    //     $wellData = [];
    //     foreach ($request->well_ids as $wellId) {
    //         $hourPrice = $request->hourly_rates[$wellId] ?? 0; // Default to zero if not provided
    //         $wellData[$wellId] = ['hour_price' => $hourPrice];
    //     }
    //     $invoice->wells()->attach($wellData);
    //     $invoice->assignHourlyRates();

    //     Alert::success('success', 'Invoice created and hourly rates assigned successfully.');
    //     return redirect()->route('invoices.index');
    // }


        /** fuction invoices to all clients  */

    // public function store(Request $request)
    // {
    //     $request->validate([
    //         'total_amount_due' => 'required|numeric',
    //         'month' => 'required|date_format:Y-m',
    //         'well_ids' => 'required|array',
    //         'well_ids.*' => 'exists:wells,id',
    //         'hourly_rates' => 'nullable|array',
    //         'hourly_rates.*' => 'nullable|numeric',
    //     ]);

    //     // Store the invoice
    //     $invoice = new Invoice();
    //     $invoice->total_amount_due = $request->total_amount_due;
    //     $invoice->month = Carbon::createFromFormat('Y-m', $request->month)->startOfMonth();
    //     $invoice->save();

    //     // Attach wells to the invoice
    //     $wellData = [];
    //     foreach ($request->well_ids as $wellId) {
    //         $hourPrice = $request->hourly_rates[$wellId] ?? null;
    //         $wellData[$wellId] = ['hour_price' => $hourPrice, 'kilowatt_reading' => $request->kilowatt_readings[$wellId]];
    //     }
    //     $invoice->wells()->attach($wellData);

    //     // Assign hourly rates
    //     $invoice->assignHourlyRates();

    //     // Distribute tax across clients
    //     $clients = Client::all();


    //     foreach ($clients as $client) {
    //         $clientPayment = new ClientPayment();
    //         $clientPayment->client_id = $client->id;
    //         $clientPayment->invoice_id = $invoice->id;
    //         $clientPayment->invoice_month = $invoice->month;
    //         $clientPayment->payment_status = 'unpaid';
    //         $amount_due = $this->calculateClientAmountDue($client, $invoice);
    //         $clientPayment->amount_due = $amount_due;
    //         $clientPayment->remaining_amount = $amount_due;
    //         $clientPayment->save();

    //         // Send notification to the client
    //         $client->notify(new InvoiceCreatedNotification($invoice, $invoice->month));
    //     }

    //     Alert::success('success', 'Invoice created and hourly rates assigned successfully.');
    //     return redirect()->route('invoices.showInvoice', ['id' => $invoice->id]);
    // }
    public function store(Request $request)
    {
        $request->validate([
            'total_amount_due' => 'nullable|numeric',
            'additional_due_amount' => 'nullable|numeric',
            'second_additional_due_amount' => 'nullable|numeric',
            'month' => 'required|date_format:Y-m',
            'well_ids' => 'required|array',
            'well_ids.*' => 'exists:wells,id',
            'hourly_rates' => 'nullable|array',
            'hourly_rates.*' => 'nullable|numeric',
        ]);

        // التحقق إذا فيه فاتورة موجودة بنفس الشهر
        $month = Carbon::parse($request->month . '-01', 'Africa/Cairo')->startOfMonth();
        $existingInvoice = Invoice::where('month', $month)->first();

        if ($existingInvoice) {
            Alert::error('Error', 'فاتورة هذا الشهر موجودة بالفعل.');
            return redirect()->back()->withInput();
        }

        // Store the invoice
        $invoice = new Invoice();
        $invoice->total_amount_due = $request->total_amount_due;
        $invoice->additional_due_amount = $request->additional_due_amount ?? 0;
        $invoice->second_additional_due_amount = $request->second_additional_due_amount ?? 0; // حفظ المبلغ الإضافي الثاني
        $invoice->month = $month;
        $invoice->save();

        // Attach wells to the invoice
        $wellData = [];
        foreach ($request->well_ids as $wellId) {
            $hourPrice = $request->hourly_rates[$wellId] ?? null;
            $wellData[$wellId] = ['hour_price' => $hourPrice, 'kilowatt_reading' => $request->kilowatt_readings[$wellId]];
        }
        $invoice->wells()->attach($wellData);

        // Assign hourly rates
        $invoice->assignHourlyRates();

        if ($invoice->total_amount_due) {
            $this->processClientInvoices($invoice);
        }
        $this->processSecondAdditionalAmount($invoice);

        Alert::success('success', 'Invoice created and hourly rates assigned successfully.');
        return redirect()->route('invoices.showInvoice', ['id' => $invoice->id]);
    }
    private function processClientInvoices(Invoice $invoice)
    {
        $clients = Client::all();

        $selectedClients = Client::whereIn('id', range(1, 58))
        ->get();

        $activeClients = $selectedClients->filter(function ($client) use ($invoice) {
            // التحقق من ساعات العميل المباشرة
            $hasClientHours = $client->hours()
                ->whereNull('deleted_at')
                ->whereIn('well_id', $invoice->wells->pluck('id'))
                ->whereBetween('start_time', [
                    $invoice->month->copy()->subMonth()->day(16)->startOfDay(),
                    $invoice->month->copy()->day(15)->endOfDay(),
                ])
                ->exists();

            // التحقق من ساعات العملاء المشاركين
            $hasCustomerHours = false;
            foreach ($client->customers as $customer) {
                if ($customer->hours()
                    ->whereNull('deleted_at')
                    ->whereIn('well_id', $invoice->wells->pluck('id'))
                    ->whereBetween('start_time', [
                        $invoice->month->copy()->subMonth()->day(16)->startOfDay(),
                        $invoice->month->copy()->day(15)->endOfDay(),
                    ])
                    ->exists()) {
                    $hasCustomerHours = true;
                    break;
                }
            }

            return $hasClientHours || $hasCustomerHours;
        });

        $totalAdditionalAmount = $invoice->additional_due_amount;
        $activeClientsCount = max($activeClients->count(), 1);
        $additionalPerActiveClient = $totalAdditionalAmount / $activeClientsCount;

        $totalClientsCount = $selectedClients->count();
        $additionalPerClient = $totalAdditionalAmount / $totalClientsCount;

        foreach ($clients as $client) {
            $clientPayment = new ClientPayment();
            $clientPayment->client_id = $client->id;
            $clientPayment->invoice_id = $invoice->id;
            $clientPayment->invoice_month = $invoice->month;
            $clientPayment->payment_status = 'unpaid';

            $amountDue = $this->calculateClientAmountDue($client, $invoice);

            // تطبيق الأرصدة السالبة من فواتير الأسباب
            $reasonBalanceApplied = $this->applyReasonBalances($client, $amountDue);
            $amountDue += $reasonBalanceApplied; // إضافة الأرصدة السالبة للمبلغ المستحق

            $previousBalance = $client->additional_due_balance;
            $additionalPaid = 0;
            $remainingAmount = $amountDue;
            $activeBonus = 0; // العمود الجديد لإضافة الـ150 جنيه
            if ($selectedClients->contains($client)) {

                if ($activeClients->contains($client)) {
                    $additionalPaid = $additionalPerActiveClient;

                    // حساب مبلغ الصيانة والتشغيل موزع على العملاء المشاركين
                    $maintenanceAmount = $this->calculateMaintenanceAmount($client, $invoice);
                    $activeBonus = $maintenanceAmount;

                    $remainingAmount += $additionalPaid + $activeBonus;
                    // لا يتم تعديل additional_due_balance هنا


                if($amountDue > 0 && $previousBalance < 0) {
                    $remainingAmount += abs($previousBalance); // إضافة الرصيد السلبي إلى المبلغ المتبقي
                    $additionalPaid = abs($previousBalance); // تسجيل الرصيد السلبي في المبلغ الإضافي
                $client->additional_due_balance = 0; // تصفير الرصيد السلبي

            }
        }

            // 2. الحالة الثانية: عميل غير نشط، رصيده موجب
            elseif ($previousBalance > 0) {
                if ($previousBalance >= $amountDue) {
                    $remainingAmount = 0;
                    $client->additional_due_balance -= $amountDue;
                } else {
                    $remainingAmount = $amountDue - $previousBalance;
                    $client->additional_due_balance = 0;
                }
            }

            // 3. حالة العملاء النشطين
            elseif ($activeClients->contains($client)) {
                $additionalPaid = $additionalPerActiveClient;
                $remainingAmount += $additionalPaid; // إضافة المبلغ الإضافي للمبلغ المتبقي
                $balanceToAdd = $additionalPaid - $additionalPerClient;
                $client->additional_due_balance += $balanceToAdd;
            }

            // 4. العملاء غير النشطين بدون مبلغ مستحق
            else {
                $additionalPaid = 0;
                $negativeBalance = -$additionalPerClient;

                if ($amountDue == 0) {
                    $client->additional_due_balance += $negativeBalance;
                }
            }
        }
            $client->save();

            $clientPayment->amount_due = $amountDue;
            $clientPayment->additional_paid = $additionalPaid;
            $clientPayment->remaining_amount = $remainingAmount;
            $clientPayment->active_bonus = $activeBonus; // حفظ مبلغ الـ150 جنيه في العمود الجديد
            $clientPayment->save();

            // إنشاء سجلات منفصلة للعملاء المشاركين
            $this->createCustomerPayments($client, $invoice, $additionalPaid, $activeBonus);

            $client->notify(new InvoiceCreatedNotification($invoice, $invoice->month));
        }
    }


    private function processSecondAdditionalAmount(Invoice $invoice)
{
    // إجمالي المبلغ الإضافي
    $totalAdditionalAmount = $invoice->second_additional_due_amount;

    // جلب جميع العملاء
    $clients = Client::all();

    // العملاء المستهدفون فقط (IDs: 1-50، 52، و53)
    $selectedClients = Client::whereIn('id', range(1, 50))
        ->orWhereIn('id', [52, 53])
        ->get();

    if ($clients->isEmpty() || $totalAdditionalAmount == 0) {
        return;
    }

    // حساب المبلغ الإضافي لكل عميل مستهدف
    $additionalPerClient = $selectedClients->isNotEmpty()
        ? $totalAdditionalAmount / $selectedClients->count()
        : 0;

    foreach ($clients as $client) {
        // جلب أحدث مدفوعات العميل
        $clientPayment = $client->clientPayments()->latest()->first();

        // التحقق من وجود مدفوعات
        if (!$clientPayment) {
            continue;
        }

        // الحصول على البيانات الأساسية
        $amountDue = $clientPayment->amount_due; // المبلغ المستحق (ثابت)
        $remainingAmount = $clientPayment->remaining_amount ?? 0; // الرصيد المتبقي الحالي
        $additionalPaid = $clientPayment->additional_paid ?? 0; // المبلغ الإضافي الحالي

        // التحقق مما إذا كان العميل مستهدفًا لإضافة المبلغ الإضافي
        if ($selectedClients->contains($client)) {
            if ($amountDue > 0) {
                // إذا كان على العميل مبلغ مستحق
                $additionalPaid += $additionalPerClient; // إضافة المبلغ الإضافي
                $remainingAmount += $additionalPerClient; // تحديث الرصيد المتبقي
            } else {
                // إذا لم يكن على العميل مبلغ مستحق
                // تحديث رصيد العميل بالسالب
                $client->additional_due_balance -= $additionalPerClient;
                $client->save();
            }

            // تحديث جدول المدفوعات فقط إذا كان على العميل مبلغ مستحق
            if ($amountDue > 0) {
                $clientPayment->additional_paid = $additionalPaid;
                $clientPayment->remaining_amount = $remainingAmount;
                $clientPayment->save();
            }
        }

        // إذا لم يكن العميل مستهدفًا، لا يتم تعديل بياناته، لكنه يظهر في الفاتورة
    }
}



// public function calculateClientAmountDue(Client $client, Invoice $invoice)
// {
//     $amountDue = 0;

//     // تحديد نطاق الفاتورة
//     $startDate = $invoice->month->copy()->subMonth()->day(16)->startOfDay(); // 16 من الشهر السابق
//     $endDate = $invoice->month->copy()->day(15)->endOfDay(); // 15 من الشهر الحالي

//     // حلقة على الآبار المرتبطة بالعميل
//     foreach ($client->wells as $well) {
//         $startTime = \Carbon\Carbon::parse($well->pivot->start_time);
//         $endTime = \Carbon\Carbon::parse($well->pivot->end_time);

//         // ضبط الأوقات داخل نطاق الفاتورة
//         $adjustedStartTime = $startTime->lessThan($startDate) ? $startDate : $startTime;
//         $adjustedEndTime = $endTime->greaterThan($endDate) ? $endDate : $endTime;

//         // حساب عدد الساعات إذا كان النطاق صالحًا
//         if ($adjustedStartTime->lt($adjustedEndTime)) {
//             $hours = $adjustedStartTime->floatDiffInHours($adjustedEndTime);
//             $amountDue += $hours * $well->pivot->hour_price;
//         }
//     }

//     return $amountDue;
// }

public function calculateClientAmountDue(Client $client, Invoice $invoice)
{
    $amountDue = 0;

    // تحديد نطاق الفاتورة
    $startDate = $invoice->month->copy()->subMonth()->day(16)->startOfDay();
    $endDate = $invoice->month->copy()->day(15)->endOfDay();

    // حساب الساعات للـ Client مباشرة
    $clientHours = $client->hours()
        ->whereNull('deleted_at')
        ->whereBetween('start_time', [$startDate, $endDate])
        ->whereIn('well_id', $invoice->wells->pluck('id'))
        ->get();

    foreach ($clientHours as $hour) {
        $startTime = Carbon::parse($hour->start_time);
        $endTime = Carbon::parse($hour->end_time);
        $hours = $startTime->floatDiffInHours($endTime);
        $amountDue += $hours * ($hour->hour_price ?? 0);
    }

    // حساب الساعات لـ Customers الخاصة بـ Client
    foreach ($client->customers as $customer) {
        $customerHours = $customer->hours()
            ->whereNull('deleted_at')
            ->whereBetween('start_time', [$startDate, $endDate])
            ->whereIn('well_id', $invoice->wells->pluck('id'))
            ->get();

        foreach ($customerHours as $hour) {
            $startTime = Carbon::parse($hour->start_time);
            $endTime = Carbon::parse($hour->end_time);
            $hours = $startTime->floatDiffInHours($endTime);
            $amountDue += $hours * ($hour->hour_price ?? 0);
        }
    }

    return $amountDue;
}

/**
 * تطبيق الأرصدة السالبة من فواتير الأسباب على العميل
 */
private function applyReasonBalances(Client $client, $amountDue, $customerId = null)
{
    $totalApplied = 0;

    // جلب الأرصدة السالبة النشطة للعميل (أساسي أو مشارك)
    $clientBalances = ClientReasonBalance::getClientActiveBalances($client->id, $customerId);

    foreach ($clientBalances as $balance) {
        $balanceAmount = abs($balance->amount);
        $appliedAmount = min($amountDue - $totalApplied, $balanceAmount);

        if ($appliedAmount > 0) {
            $totalApplied += $appliedAmount;

            // تحديث الرصيد
            $newAmount = $balanceAmount - $appliedAmount;
            if ($newAmount <= 0) {
                $balance->update(['status' => 'used']);
            } else {
                $balance->update(['amount' => -$newAmount]);
            }
        }

        if ($totalApplied >= $amountDue) break;
    }

    return $totalApplied;
}

/**
 * حساب مبلغ الصيانة والتشغيل موزع على العملاء المشاركين
 */
private function calculateMaintenanceAmount(Client $client, Invoice $invoice)
{
    $baseMaintenance = 150; // المبلغ الأساسي

    // تحديد نطاق الفاتورة
    $startDate = $invoice->month->copy()->subMonth()->day(16)->startOfDay();
    $endDate = $invoice->month->copy()->day(15)->endOfDay();

    // حساب عدد العملاء المشاركين الذين لديهم ساعات
    $activeCustomersCount = 0;
    $hasClientHours = $client->hours()
        ->whereNull('deleted_at')
        ->whereBetween('start_time', [$startDate, $endDate])
        ->whereIn('well_id', $invoice->wells->pluck('id'))
        ->exists();

    if ($hasClientHours) {
        $activeCustomersCount++; // العميل الأساسي
    }

    foreach ($client->customers as $customer) {
        $hasCustomerHours = $customer->hours()
            ->whereNull('deleted_at')
            ->whereBetween('start_time', [$startDate, $endDate])
            ->whereIn('well_id', $invoice->wells->pluck('id'))
            ->exists();

        if ($hasCustomerHours) {
            $activeCustomersCount++;
        }
    }

    // إذا كان هناك عملاء مشاركين، قسم المبلغ
    if ($activeCustomersCount > 1) {
        return $baseMaintenance / $activeCustomersCount;
    }

    // إذا لم يكن هناك عملاء مشاركين، المبلغ كامل للعميل الأساسي
    return $baseMaintenance;
}

/**
 * إنشاء سجلات منفصلة للعملاء المشاركين مع توزيع المبالغ الإضافية والصيانة
 */
private function createCustomerPayments(Client $client, Invoice $invoice, $additionalPaid, $activeBonus)
{
    // تحديد نطاق الفاتورة
    $startDate = $invoice->month->copy()->subMonth()->day(16)->startOfDay();
    $endDate = $invoice->month->copy()->day(15)->endOfDay();

    // جلب العملاء المشاركين الذين لديهم ساعات
    $activeCustomers = [];
    foreach ($client->customers as $customer) {
        $customerHours = $customer->hours()
            ->whereNull('deleted_at')
            ->whereBetween('start_time', [$startDate, $endDate])
            ->whereIn('well_id', $invoice->wells->pluck('id'))
            ->get();

        if ($customerHours->count() > 0) {
            $customerAmountDue = 0;
            foreach ($customerHours as $hour) {
                $start = Carbon::parse($hour->start_time);
                $end = Carbon::parse($hour->end_time);
                $hours = $start->floatDiffInHours($end);
                $customerAmountDue += $hours * ($hour->hour_price ?? 0);
            }

            $activeCustomers[] = [
                'customer' => $customer,
                'amount_due' => $customerAmountDue,
                'hours_count' => $customerHours->count()
            ];
        }
    }

    // إذا كان هناك عملاء مشاركين، قسم المبالغ الإضافية والصيانة عليهم
    if (!empty($activeCustomers)) {
        $customersCount = count($activeCustomers);
        $additionalPerCustomer = $additionalPaid / ($customersCount + 1); // +1 للعميل الأساسي
        $bonusPerCustomer = $activeBonus / ($customersCount + 1);

        foreach ($activeCustomers as $customerData) {
            $customer = $customerData['customer'];
            $customerAmountDue = $customerData['amount_due'];

            // تطبيق الأرصدة السالبة للعميل المشارك
            $reasonBalanceApplied = $this->applyReasonBalances($client, $customerAmountDue, $customer->id);
            $customerAmountDue += $reasonBalanceApplied;

            $customerRemainingAmount = $customerAmountDue + $additionalPerCustomer + $bonusPerCustomer;

            ClientPayment::create([
                'client_id' => $client->id,
                'customer_id' => $customer->id,
                'invoice_id' => $invoice->id,
                'invoice_month' => $invoice->month,
                'payment_status' => 'unpaid',
                'amount_due' => $customerAmountDue,
                'additional_paid' => $additionalPerCustomer,
                'remaining_amount' => $customerRemainingAmount,
                'active_bonus' => $bonusPerCustomer
            ]);
        }

        // تحديث مبالغ العميل الأساسي (خصم نصيب العملاء المشاركين)
        $mainClientPayment = ClientPayment::where('invoice_id', $invoice->id)
            ->where('client_id', $client->id)
            ->whereNull('customer_id')
            ->first();

        if ($mainClientPayment) {
            $mainClientPayment->additional_paid = $additionalPerCustomer;
            $mainClientPayment->active_bonus = $bonusPerCustomer;
            $mainClientPayment->remaining_amount = $mainClientPayment->amount_due + $additionalPerCustomer + $bonusPerCustomer;
            $mainClientPayment->save();
        }
    }
}

// public function showInvoice(Request $request, $id)
// {
//     $invoice = Invoice::findOrFail($id);

//     // حساب فترة الفاتورة
//     $startDate = $invoice->month->copy()->subMonth()->day(16)->startOfDay();
//     $endDate = $invoice->month->copy()->day(15)->endOfDay();

//     // استرجاع فلتر الحالة من الطلب
//     $filterStatus = $request->get('filter_status');

//     // تحميل البيانات مع العلاقات
//     $invoice = Invoice::with([
//         'clientPayments' => function ($query) use ($filterStatus) {
//             $query->select('invoice_id', 'client_id', 'remaining_amount', 'amount_due', 'payment_status', 'additional_paid','active_bonus')
//                 ->with('client:id,name');

//             // تطبيق الفلتر
//             if ($filterStatus == 'paid') {
//                 $query->where('payment_status', 'paid');
//             } elseif ($filterStatus == 'unpaid') {
//                 $query->where('payment_status', 'unpaid');
//             } elseif ($filterStatus == 'has_due') {
//                 $query->where('amount_due', '>', 0);
//             } elseif ($filterStatus == 'no_due') {
//                 $query->where('amount_due', '<=', 0);
//             } elseif ($filterStatus == 'has_duee') {
//                 $query->where('additional_paid', '>', 0);
//             } elseif ($filterStatus == 'no_duee') {
//                 $query->where('additional_paid', '<=', 0)
//                       ->where('amount_due', '>', 0);
//             }

//         },
//         'wells' => function ($query) use ($startDate, $endDate) {
//             $query->withPivot('hour_price', 'kilowatt_reading')
//                 ->with(['hours' => function ($query) use ($startDate, $endDate) {
//                     $query->whereBetween('start_time', [$startDate, $endDate]);
//                 }]);
//         }
//     ])->findOrFail($id);


//     // حساب عدد الساعات لكل عميل ولكل بئر
//     $clientWorkData = [];
//     foreach ($invoice->wells as $well) {
//         foreach ($well->hours as $hour) {
//             $clientId = $hour->client_id;
//             if (!isset($clientWorkData[$clientId])) {
//                 $clientWorkData[$clientId] = [
//                     'total_hours' => 0,
//                     'wells' => [],
//                 ];
//             }

//             $clientWorkData[$clientId]['total_hours'] += $hour->hours_worked;
//             if (!isset($clientWorkData[$clientId]['wells'][$well->id])) {
//                 $clientWorkData[$clientId]['wells'][$well->id] = [
//                     'name' => $well->name,
//                     'hours' => 0,
//                 ];
//             }

//             $clientWorkData[$clientId]['wells'][$well->id]['hours'] += $hour->hours_worked;
//         }
//     }

//     return view('invoices.showInvoice', compact('invoice', 'clientWorkData','filterStatus'));
// }
public function showInvoice(Request $request, $id)
{
    $invoice = Invoice::findOrFail($id);

    // حساب فترة الفاتورة
    $startDate = $invoice->month->copy()->subMonth()->day(16)->startOfDay();
    $endDate = $invoice->month->copy()->day(15)->endOfDay();

    // استرجاع فلتر الحالة من الطلب
    $filterStatus = $request->get('filter_status');

    // تحميل البيانات مع العلاقات
    $invoice = Invoice::with([
        'clientPayments' => function ($query) use ($filterStatus) {
            $query->select('invoice_id', 'client_id', 'customer_id', 'remaining_amount', 'amount_due', 'payment_status', 'additional_paid', 'active_bonus')
                ->with(['client:id,name', 'customer:id,name']);
            if ($filterStatus == 'paid') {
                $query->where('payment_status', 'paid');
            } elseif ($filterStatus == 'unpaid') {
                $query->where('payment_status', 'unpaid');
            } elseif ($filterStatus == 'has_due') {
                $query->where('amount_due', '>', 0);
            } elseif ($filterStatus == 'no_due') {
                $query->where('amount_due', '<=, 0');
            }
        },
        'wells' => function ($query) use ($startDate, $endDate) {
            $query->withPivot('hour_price', 'kilowatt_reading')
                ->with(['hours' => function ($query) use ($startDate, $endDate) {
                    $query->whereBetween('start_time', [$startDate, $endDate])
                          ->whereNull('deleted_at') // استثناء السجلات المحذوفة بـ soft deletes
                          ->with('client', 'customer') // تحميل العلاقات مع الكلاينت والكاستمر
                          ->select('well_id', 'client_id', 'customer_id', 'start_time', 'end_time', 'hour_price', 'amount_due');
                }]);
        }
    ])->findOrFail($id);

    // جمع بيانات الكلاينت والكاستمرز في نفس الصف
    $combinedWorkData = [];

    foreach ($invoice->clientPayments as $payment) {
        $clientId = $payment->client_id;
        $combinedWorkData[$clientId] = [
            'client_name' => $payment->client->name,
            'customer_data' => [], // لتخزين بيانات الكاستمرز
            'client_hours' => 0, // ساعات الكلاينت فقط
            'customer_hours' => 0, // إجمالي ساعات الكاستمرز
            'total_hours' => 0, // إجمالي الساعات (كلاينت + كاستمر)
            'client_amount_due' => 0, // المبلغ المستحق للكلاينت فقط
            'customer_amount_due' => 0, // إجمالي المبلغ المستحق للكاستمرز
            'remaining_amount' => 0, // سوف نحسبه كمجموع
            'payment_status' => $payment->payment_status,
            'additional_paid' => $payment->additional_paid ?? 0,
            'active_bonus' => $payment->active_bonus ?? 0,
            'client_wells' => [], // لتخزين تفاصيل الآبار للكلاينت
            'customer_wells' => [], // لتخزين تفاصيل الآبار للكاستمرز
        ];
    }

    // حساب الساعات والمبالغ من جدول hours باستخدام start_time و end_time
    foreach ($invoice->wells as $well) {
        foreach ($well->hours as $hour) {
            $clientId = $hour->client_id;
            $customerId = $hour->customer_id;

            // حساب الساعات من الفرق بين start_time و end_time
            $hours = Carbon::parse($hour->start_time)->floatDiffInHours(Carbon::parse($hour->end_time));
            $amount = $hour->amount_due ?? ($hours * $well->pivot->hour_price); // استخدام amount_due إن وجد، أو حسابه

            if (!$customerId) {
                // بيانات الكلاينت
                $combinedWorkData[$clientId]['client_hours'] += $hours;
                $combinedWorkData[$clientId]['total_hours'] += $hours;
                if (!isset($combinedWorkData[$clientId]['client_wells'][$well->id])) {
                    $combinedWorkData[$clientId]['client_wells'][$well->id] = [
                        'name' => $well->name,
                        'hours' => 0,
                        'hour_price' => $well->pivot->hour_price,
                    ];
                }
                $combinedWorkData[$clientId]['client_wells'][$well->id]['hours'] += $hours;
                $combinedWorkData[$clientId]['client_amount_due'] += $amount; // إضافة المبلغ للكلاينت فقط
            } else {
                // بيانات الكاستمر
                if (!isset($combinedWorkData[$clientId]['customer_data'][$customerId])) {
                    $combinedWorkData[$clientId]['customer_data'][$customerId] = [
                        'name' => $hour->customer->name ?? 'Customer ' . $customerId,
                        'amount_due' => 0,
                        'hours' => 0,
                        'wells' => [],
                    ];
                }
               $combinedWorkData[$clientId]['customer_data'][$customerId]['hours'] += $hours;
                $combinedWorkData[$clientId]['customer_hours'] += $hours;
                $combinedWorkData[$clientId]['total_hours'] += $hours;
                $combinedWorkData[$clientId]['customer_data'][$customerId]['amount_due'] += $amount;
                $combinedWorkData[$clientId]['customer_amount_due'] += $amount;

                if (!isset($combinedWorkData[$clientId]['customer_data'][$customerId]['wells'][$well->id])) {
                    $combinedWorkData[$clientId]['customer_data'][$customerId]['wells'][$well->id] = [
                        'name' => $well->name,
                        'hours' => 0,
                        'hour_price' => $hour->hour_price ?? $well->pivot->hour_price,
                    ];
                }
                $combinedWorkData[$clientId]['customer_data'][$customerId]['wells'][$well->id]['hours'] += $hours;
                $combinedWorkData[$clientId]['customer_wells'][$well->id] = [
                    'name' => $well->name,
                    'hours' => $hours,
                    'hour_price' => $hour->hour_price ?? $well->pivot->hour_price,
                ];
            }
        }
    }

    // حساب المبلغ المتبقي كمجموع المبالغ المستحقة للكلاينت والكاستمرز + المبالغ الإضافية
    foreach ($combinedWorkData as $clientId => &$data) {
        // المبلغ المستحق للكلاينت يبقى كما هو (للساعات الخاصة بيه فقط)
        // المبلغ المستحق للكاستمرز يبقى كما هو (للساعات الخاصة بكل كاستمر)
        // المبلغ المتبقي = مجموع المبلغ المستحق للكلاينت + المبلغ المستحق للكاستمرز + المبالغ الإضافية
        $totalAmountDue = $data['client_amount_due'] + $data['customer_amount_due'] + $data['additional_paid'] + $data['active_bonus'];
        $data['remaining_amount'] = $totalAmountDue; // نضع المبلغ المتبقي كمجموع
    }

    // فصل بيانات العملاء المشاركين
    $separatedPayments = [];
    foreach ($invoice->clientPayments as $payment) {
        if ($payment->customer_id) {
            // عميل مشارك
            $separatedPayments[] = [
                'type' => 'customer',
                'client_id' => $payment->client_id,
                'customer_id' => $payment->customer_id,
                'client_name' => $payment->client->name,
                'customer_name' => $payment->customer->name,
                'amount_due' => $payment->amount_due,
                'additional_paid' => $payment->additional_paid,
                'active_bonus' => $payment->active_bonus,
                'remaining_amount' => $payment->remaining_amount,
                'payment_status' => $payment->payment_status
            ];
        } else {
            // عميل أساسي
            $separatedPayments[] = [
                'type' => 'main_client',
                'client_id' => $payment->client_id,
                'customer_id' => null,
                'client_name' => $payment->client->name,
                'customer_name' => null,
                'amount_due' => $payment->amount_due,
                'additional_paid' => $payment->additional_paid,
                'active_bonus' => $payment->active_bonus,
                'remaining_amount' => $payment->remaining_amount,
                'payment_status' => $payment->payment_status
            ];
        }
    }

    return view('invoices.showInvoice', compact('invoice', 'combinedWorkData', 'filterStatus', 'separatedPayments'));
}





    public function show(Invoice $invoice)
{
    $invoice->load('wells', 'client.invoices');

    $wellsData = [];
    $totalAmountDue = 0;

    foreach ($invoice->wells as $well) {
        // Calculate the total hours worked for this well in the given month
        $hours = $well->hours()
            ->whereMonth('start_time', $invoice->month->month)
            ->whereYear('start_time', $invoice->month->year)
            ->get();

        $wellTotalHours = $hours->sum(function ($hour) {
            $start = \Carbon\Carbon::parse($hour->start_time);
            $end = \Carbon\Carbon::parse($hour->end_time);
            return $start->floatDiffInHours($end);
        });

        $amountDue = $wellTotalHours * $well->pivot->hour_price;
        $totalAmountDue += $amountDue;

        $wellsData[] = [
            'name' => $well->name,
            'hours' => $wellTotalHours,
            'hourly_rate' => $well->pivot->hour_price,
            'amount_due' => $amountDue,
        ];
    }

    $invoice->total_amount_due = $totalAmountDue; // Update total amount due for display

    return view('invoices.show', compact('invoice', 'wellsData'));
}




// public function clientsWithInvoices()
// {
//     // Fetch clients with their wells and associated invoices
//     $clients = Client::with(['wells.invoices'])->get();

//     // Group invoices by client and month
//     $clients->each(function ($client) {
//         $monthlyInvoices = [];

//         // Loop through each well and its invoices
//         foreach ($client->wells as $well) {
//             foreach ($well->invoices as $invoice) {
//                 $month = $invoice->month->format('F Y');

//                 // Group invoices by month
//                 if (!isset($monthlyInvoices[$month])) {
//                     $monthlyInvoices[$month] = [];
//                 }

//                 $monthlyInvoices[$month][$invoice->id] = $invoice;
//             }
//         }

//         // Assign the grouped invoices to the client
//         $client->monthly_invoices = $monthlyInvoices;
//     });

//     return view('invoices.clients_with_invoices', compact('clients'));
// }






    public function edit(Invoice $invoice)
    {
        $wells = Well::all();
        return view('invoices.edit', compact('invoice', 'wells'));
    }

    public function update(Request $request, $id)
{
    $request->validate([
        'total_amount_due' => 'nullable|numeric',
        'month' => 'required|date_format:Y-m',
        'well_ids' => 'required|array',
        'well_ids.*' => 'exists:wells,id',
        'kilowatt_readings' => 'nullable|array',
        'kilowatt_readings.*' => 'nullable|numeric',
    ]);

    $invoice = Invoice::findOrFail($id);

    // إذا كان المبلغ الإضافي قد تغير، نحتاج إلى إعادة الحسابات
    $oldAdditionalDueAmount = $invoice->additional_due_amount;

    // Update invoice details
    $invoice->total_amount_due = $request->total_amount_due;
    $invoice->month = Carbon::parse($request->month . '-01', 'Africa/Cairo')->startOfMonth();
    $invoice->additional_due_amount = $request->additional_due_amount ?? 0;
    $invoice->save();

    // Update wells and their data
    $wellData = [];
    foreach ($request->well_ids as $wellId) {
        $wellData[$wellId] = ['kilowatt_reading' => $request->kilowatt_readings[$wellId] ?? null];
    }
    $invoice->wells()->sync($wellData);

    // Reassign hourly rates
    $invoice->assignHourlyRates();

    // إذا كان المبلغ الإضافي قد تغير، نحتاج لإزالة المبالغ السابقة وإعادة الحسابات بناءً على المبلغ الجديد
    if ($oldAdditionalDueAmount != $invoice->additional_due_amount) {
        $this->recalculateClientPayments($invoice);
    } else {
        $this->updateClientInvoices($invoice);
    }

    Alert::success('success', 'Invoice updated successfully.');
    return redirect()->route('invoices.showInvoice', ['id' => $invoice->id]);
}


private function updateClientInvoices(Invoice $invoice)
{
    // جلب جميع العملاء
    $clients = Client::all();

    // تحديد المبلغ الإضافي للعميل النشط
    $totalAdditionalAmount = $invoice->additional_due_amount;
    $activeClients = $clients->filter(function ($client) use ($invoice) {
        // التحقق من ساعات العميل المباشرة
        $hasClientHours = $client->hours()
            ->whereNull('deleted_at')
            ->whereIn('well_id', $invoice->wells->pluck('id'))
            ->whereBetween('start_time', [
                $invoice->month->copy()->subMonth()->day(16)->startOfDay(),
                $invoice->month->copy()->day(15)->endOfDay(),
            ])
            ->exists();

        // التحقق من ساعات العملاء المشاركين
        $hasCustomerHours = false;
        foreach ($client->customers as $customer) {
            if ($customer->hours()
                ->whereNull('deleted_at')
                ->whereIn('well_id', $invoice->wells->pluck('id'))
                ->whereBetween('start_time', [
                    $invoice->month->copy()->subMonth()->day(16)->startOfDay(),
                    $invoice->month->copy()->day(15)->endOfDay(),
                ])
                ->exists()) {
                $hasCustomerHours = true;
                break;
            }
        }

        return $hasClientHours || $hasCustomerHours;
    });

    $activeClientsCount = max($activeClients->count(), 1); // لا يمكن أن يكون العدد صفرًا
    $additionalPerActiveClient = $totalAdditionalAmount / $activeClientsCount;

    // لكل عميل نقوم بتحديث معلومات الدفع الخاصة به
    foreach ($clients as $client) {
        $clientPayment = ClientPayment::where('client_id', $client->id)
            ->where('invoice_id', $invoice->id)
            ->first();

        // حساب المبلغ المستحق للعميل
        $amount_due = $this->calculateClientAmountDue($client, $invoice);
        $previousBalance = $client->additional_due_balance;

        $remainingAmount = $amount_due;
        $additionalPaid = 0;
        $activeBonus = 0; // العمود الجديد لإضافة الـ150 جنيه

        // حالة العميل: عميل نشط
        if ($activeClients->contains($client)) {
            $additionalPaid = $additionalPerActiveClient;
            $activeBonus = 150; // مبلغ الـ150 جنيه للعملاء النشيطين فقط
            $remainingAmount += $additionalPaid + $activeBonus; // إضافة المبلغ الإضافي والـ150 جنيه للمبلغ المتبقي
            $balanceToAdd = $additionalPaid - ($previousBalance);
            $client->additional_due_balance += $balanceToAdd; // تحديث الرصيد الإضافي
        } elseif ($previousBalance < 0 && $amount_due > 0) {
            // حالة العميل مع رصيد سلبي
            $remainingAmount += abs($previousBalance); // إضافة الرصيد السلبي للمبلغ المستحق
            $client->additional_due_balance = 0; // تصفير الرصيد السلبي
        }

        // تحديث أو إنشاء سجل الدفع
        if ($clientPayment) {
            $clientPayment->amount_due = $amount_due;
            $clientPayment->additional_paid = $additionalPaid;
            $clientPayment->remaining_amount = $remainingAmount;
            $clientPayment->active_bonus = $activeBonus; // حفظ مبلغ الـ150 جنيه في العمود الجديد
            $clientPayment->save();
        } else {
            $clientPayment = new ClientPayment();
            $clientPayment->client_id = $client->id;
            $clientPayment->invoice_id = $invoice->id;
            $clientPayment->invoice_month = $invoice->month;
            $clientPayment->payment_status = 'unpaid';
            $clientPayment->amount_due = $amount_due;
            $clientPayment->additional_paid = $additionalPaid;
            $clientPayment->remaining_amount = $remainingAmount;
            $clientPayment->active_bonus = $activeBonus; // حفظ مبلغ الـ150 جنيه في العمود الجديد
            $clientPayment->save();
        }

        // حفظ تغييرات العميل
        $client->save();

        // إخطار العميل بإنشاء الفاتورة
        $client->notify(new InvoiceCreatedNotification($invoice, $invoice->month));
    }
}


private function recalculateClientPayments(Invoice $invoice)
{
    // استرجاع جميع العملاء
    $clients = Client::all();

    // حذف المبالغ القديمة من جدول المدفوعات
    foreach ($clients as $client) {
        $clientPayment = ClientPayment::where('client_id', $client->id)
            ->where('invoice_id', $invoice->id)
            ->first();

        if ($clientPayment) {
            $clientPayment->delete(); // حذف المدفوعات القديمة
        }
    }

    // إعادة حساب المبالغ بناءً على المبلغ الإضافي الجديد
    $this->processClientInvoices($invoice); // إعادة توزيع المبلغ الإضافي الجديد على العملاء
}


    public function destroy(Invoice $invoice)
    {
        $invoice->delete();

        Alert::success('success', 'Invoice deleted successfully.');
        return redirect()->route('invoices.index');
    }
}
