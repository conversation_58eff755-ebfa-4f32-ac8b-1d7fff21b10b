<?php $__env->startSection('css'); ?>
    <style>
        @media print {
            /* إجبار الجدول على عدم الانقسام بين الصفحات */
            .table-responsive {
                display: block !important;
                width: 100% !important;
                overflow-x: auto !important;
                -webkit-print-color-adjust: exact !important;
            }

            .table {
                width: 100% !important;
                page-break-inside: auto !important;
                page-break-before: auto !important;
                page-break-after: auto !important;
            }

            /* منع تقسيم الصفوف بين الصفحات */
            tr {
                page-break-inside: avoid !important;
                page-break-before: auto !important;
                page-break-after: auto !important;
            }

            /* جعل الصفحة مطولة ومنع التكرار على صفحات متعددة */
            body {
                height: auto !important;
                overflow: visible !important;
            }

            /* إخفاء العناصر غير الضرورية أثناء الطباعة */
            .d-print-none {
                display: none !important;
            }

            /* تقليل الهوامش */
            /*@page {*/
                margin: 5mm !important; /* تقليل الهوامش لاستيعاب محتوى أكبر
            }

            /* جعل الكارد يظهر بشكل كامل دون تقسيم */
            .card {
                page-break-inside: avoid !important;
            }

            /* تقليل حجم الخطوط لاستيعاب 56 عميل */
            .table th, .table td {
                font-size: 10px !important; /* تقليل حجم الخط */
                padding: 4px !important; /* تقليل المسافات الداخلية */
            }

            /* جعل الجدول يظهر بشكل أفقي أفضل */
            .table-responsive {
                white-space: nowrap !important; /* منع التفاف النصوص */
            }
        }
    </style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <?php $__env->startComponent('components.breadcrumb'); ?>
        <?php $__env->slot('li_1'); ?>
            <?php echo e(__('showInvoice.management')); ?>

        <?php $__env->endSlot(); ?>
        <?php $__env->slot('title'); ?>
            <?php echo e(__('showInvoice.invoice_details')); ?>

        <?php $__env->endSlot(); ?>
    <?php echo $__env->renderComponent(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <!-- Success and Error Messages -->
                    <?php if(session('success')): ?>
                        <div class="alert alert-success"><?php echo e(session('success')); ?></div>
                    <?php elseif(session('error')): ?>
                        <div class="alert alert-danger"><?php echo e(session('error')); ?></div>
                    <?php endif; ?>

                    <!-- Invoice Summary -->
                    <div class="invoice-summary mb-4 text-center">
                        <h4 class="card-title mb-3"><?php echo e(__('showInvoice.invoice_summary')); ?></h4>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="fs-5">
                                    <strong><?php echo e(__('showInvoice.total_amount_due')); ?></strong>
                                    <?php echo e(number_format($invoice->total_amount_due, 2)); ?> <?php echo e(__('showInvoice.currency')); ?>

                                </p>
                            </div>
                            <div class="col-md-6">
                                <?php
                                    // Parse the start and end dates from the invoice
                                    $formattedStartDate = \Carbon\Carbon::parse($invoice->start_date);
                                    $formattedEndDate = \Carbon\Carbon::parse($invoice->end_date);

                                    // Check the current locale
                                    if (app()->getLocale() == 'ae') {
                                        // Arabic locale: Format the dates with Arabic numerals and Arabic text
                                        $formattedStartDate = $formattedStartDate->locale('ar')->translatedFormat('d F Y');
                                        $formattedEndDate = $formattedEndDate->locale('ar')->translatedFormat('d F Y');
                                        $separator = 'إلى'; // Arabic 'to'

                                        // Convert numbers to Arabic script
                                        $formattedStartDate = preg_replace_callback('/\d/', function ($matches) {
                                            return convertToArabicNumbers($matches[0]);
                                        }, $formattedStartDate);

                                        $formattedEndDate = preg_replace_callback('/\d/', function ($matches) {
                                            return convertToArabicNumbers($matches[0]);
                                        }, $formattedEndDate);

                                    } else {
                                        // English locale: Format the dates in standard English
                                        $formattedStartDate = $formattedStartDate->format('F d, Y');
                                        $formattedEndDate = $formattedEndDate->format('F d, Y');
                                        $separator = 'to'; // English 'to'
                                    }
                                ?>

                                <p class="fs-5">
                                    <strong><?php echo e(__('showInvoice.period')); ?></strong>
                                    <?php echo e($formattedStartDate); ?> <?php echo e($separator); ?> <?php echo e($formattedEndDate); ?>

                                </p>
                            </div>
                            <div class="col-md-6">
                                <p class="fs-5"><strong><?php echo e(__('showInvoice.reason')); ?></strong> <?php echo e($invoice->reason); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Wells Information -->
                    <?php if(!empty($wellsInfo)): ?>
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">معلومات الآبار وأسعار الساعات</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover align-middle table-bordered mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>اسم البئر</th>
                                            <th>إجمالي الساعات</th>
                                            <th>النسبة المئوية</th>
                                            <th>المبلغ المخصص</th>
                                            <th>سعر الساعة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $wellsInfo; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $wellInfo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><strong><?php echo e($wellInfo['name']); ?></strong></td>
                                            <td><?php echo e(number_format($wellInfo['total_hours'], 2)); ?> ساعة</td>
                                            <td><?php echo e(number_format($wellInfo['percentage'], 2)); ?>%</td>
                                            <td><?php echo e(number_format($wellInfo['allocated_amount'], 2)); ?> جنيه</td>
                                            <td><strong><?php echo e(number_format($wellInfo['hourly_rate'], 2)); ?> جنيه/ساعة</strong></td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Client Breakdown Table -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><?php echo e(__('showInvoice.client_breakdown')); ?></h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover align-middle table-bordered mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="8%">المعرف</th>
                                            <th width="20%">اسم العميل/المشارك</th>
                                            <th width="10%">النوع</th>
                                            <th width="12%">الساعات</th>
                                            <th width="15%">تفاصيل الآبار</th>
                                            <th width="15%">المبلغ المستحق</th>
                                            <th width="15%">إجمالي العميل</th>
                                            <th width="10%">حالة الدفع</th>
                                            <th width="10%">المتبقي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $clientData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            
                                            <tr>
                                                <td class="text-center fw-bold"><?php echo e($data['client_id']); ?></td>
                                                <td>
                                                    <strong><?php echo e($data['client']); ?></strong>
                                                    <br><small class="text-muted">العميل الأساسي</small>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge bg-secondary">أساسي</span>
                                                </td>
                                                <td class="text-center">
                                                    <?php
                                                        $clientHours = $data['client_hours'];
                                                        $totalHours = floor($clientHours);
                                                        $minutes = round(($clientHours - $totalHours) * 60);
                                                        if ($minutes >= 60) {
                                                            $totalHours += floor($minutes / 60);
                                                            $minutes = $minutes % 60;
                                                        }
                                                        $formattedHours = convertToArabicNumbers($totalHours);
                                                        $formattedMinutes = convertToArabicNumbers($minutes);
                                                    ?>
                                                    <span class="fw-bold"><?php echo e($formattedHours); ?> ساعة و <?php echo e($formattedMinutes); ?> دقيقة</span>
                                                </td>
                                                <td class="text-center">
                                                    <?php if(!empty($data['client_wells'])): ?>
                                                        <?php $__currentLoopData = $data['client_wells']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $well): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="mb-1">
                                                                <strong><?php echo e($well['name']); ?>:</strong>
                                                                <?php
                                                                    $wellHours = $well['hours'];
                                                                    $hours = floor($wellHours);
                                                                    $minutes = round(($wellHours - $hours) * 60);
                                                                    if ($minutes >= 60) {
                                                                        $hours += floor($minutes / 60);
                                                                        $minutes = $minutes % 60;
                                                                    }
                                                                    $formattedHours = convertToArabicNumbers($hours);
                                                                    $formattedMinutes = convertToArabicNumbers($minutes);
                                                                ?>
                                                                <?php echo e($formattedHours); ?> ساعة و <?php echo e($formattedMinutes); ?> دقيقة
                                                                <br><small class="text-muted">(<?php echo e(number_format($well['hourly_rate'], 2)); ?> جنيه/ساعة)</small>
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-center">
                                                    <span class="fw-bold"><?php echo e(number_format($data['client_amount'], 2)); ?></span>
                                                    <br><small class="text-muted">جنيه</small>
                                                </td>
                                                <td class="text-center" rowspan="<?php echo e(count($data['customer_details']) + 1); ?>">
                                                    <div class="border rounded p-2" style="background-color: #f8f9fa;">
                                                        <strong class="fs-5"><?php echo e(number_format($data['total_amount'], 2)); ?></strong>
                                                        <br><small class="text-muted">جنيه</small>
                                                        <hr class="my-1">
                                                        <?php
                                                            $totalHours = $data['total_hours'];
                                                            $hours = floor($totalHours);
                                                            $minutes = round(($totalHours - $hours) * 60);
                                                            if ($minutes >= 60) {
                                                                $hours += floor($minutes / 60);
                                                                $minutes = $minutes % 60;
                                                            }
                                                            $formattedHours = convertToArabicNumbers($hours);
                                                            $formattedMinutes = convertToArabicNumbers($minutes);
                                                        ?>
                                                        <small class="text-muted"><?php echo e($formattedHours); ?> ساعة و <?php echo e($formattedMinutes); ?> دقيقة</small>
                                                    </div>
                                                </td>
                                                <td class="text-center" rowspan="<?php echo e(count($data['customer_details']) + 1); ?>">
                                                    <?php if($data['payment_status'] == 'paid'): ?>
                                                        <span class="badge bg-success fs-6">مدفوع</span>
                                                    <?php elseif($data['payment_status'] == 'partial'): ?>
                                                        <span class="badge bg-warning fs-6">جزئي</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger fs-6">غير مدفوع</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-center" rowspan="<?php echo e(count($data['customer_details']) + 1); ?>">
                                                    <span class="fw-bold"><?php echo e(number_format($data['remaining_amount'], 2)); ?></span>
                                                    <br><small class="text-muted">جنيه</small>
                                                </td>
                                            </tr>

                                            
                                            <?php $__currentLoopData = $data['customer_details']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr style="background-color: #f8f9fa;">
                                                    <td class="text-center">-</td>
                                                    <td>
                                                        <span class="fw-bold"><?php echo e($customer['name']); ?></span>
                                                        <br><small class="text-muted">عميل مشارك</small>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge bg-light text-dark">مشارك</span>
                                                    </td>
                                                    <td class="text-center">
                                                        <?php
                                                            $customerHours = $customer['hours'];
                                                            $totalHours = floor($customerHours);
                                                            $minutes = round(($customerHours - $totalHours) * 60);
                                                            if ($minutes >= 60) {
                                                                $totalHours += floor($minutes / 60);
                                                                $minutes = $minutes % 60;
                                                            }
                                                            $formattedHours = convertToArabicNumbers($totalHours);
                                                            $formattedMinutes = convertToArabicNumbers($minutes);
                                                        ?>
                                                        <span class="fw-bold"><?php echo e($formattedHours); ?> ساعة و <?php echo e($formattedMinutes); ?> دقيقة</span>
                                                    </td>
                                                    <td class="text-center">
                                                        <?php if(!empty($customer['wells'])): ?>
                                                            <?php $__currentLoopData = $customer['wells']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $well): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <div class="mb-1">
                                                                    <strong><?php echo e($well['name']); ?>:</strong>
                                                                    <?php
                                                                        $wellHours = $well['hours'];
                                                                        $hours = floor($wellHours);
                                                                        $minutes = round(($wellHours - $hours) * 60);
                                                                        if ($minutes >= 60) {
                                                                            $hours += floor($minutes / 60);
                                                                            $minutes = $minutes % 60;
                                                                        }
                                                                        $formattedHours = convertToArabicNumbers($hours);
                                                                        $formattedMinutes = convertToArabicNumbers($minutes);
                                                                    ?>
                                                                    <?php echo e($formattedHours); ?> ساعة و <?php echo e($formattedMinutes); ?> دقيقة
                                                                    <br><small class="text-muted">(<?php echo e(number_format($well['hourly_rate'], 2)); ?> جنيه/ساعة)</small>
                                                                </div>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="fw-bold"><?php echo e(number_format($customer['amount'], 2)); ?></span>
                                                        <br><small class="text-muted">جنيه</small>
                                                    </td>
                                                    
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار إدارة الأرصدة -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">إدارة الأرصدة السالبة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <p class="mb-2"><strong>حالة الأرصدة:</strong>
                                                <?php if($balanceStatus): ?>
                                                    <span class="badge bg-success">تم إضافة المبالغ للأرصدة السالبة</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">لم يتم إضافة المبالغ للأرصدة بعد</span>
                                                <?php endif; ?>
                                            </p>
                                            <p class="text-muted small">
                                                عند إضافة المبالغ للأرصدة، سيتم إضافة مبلغ سالب لكل عميل وعميل مشارك
                                                ليتم خصمه تلقائياً من الفواتير العادية القادمة.
                                            </p>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <?php if(!$balanceStatus): ?>
                                                <form action="<?php echo e(route('invoice-reason.add-to-balances', $invoice->id)); ?>" method="POST" style="display: inline;">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="btn btn-success"
                                                            onclick="return confirm('هل أنت متأكد من إضافة هذه المبالغ للأرصدة السالبة؟')">
                                                        <i class="mdi mdi-plus-circle"></i> إضافة للأرصدة
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <form action="<?php echo e(route('invoice-reason.remove-from-balances', $invoice->id)); ?>" method="POST" style="display: inline;">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="btn btn-danger"
                                                            onclick="return confirm('هل أنت متأكد من إلغاء الأرصدة السالبة؟')">
                                                        <i class="mdi mdi-minus-circle"></i> إلغاء من الأرصدة
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Summary Section -->
                    <div class="row justify-content-center text-center mt-5">
                        <!-- Total Amount -->
                        <div class="col-md-3">
                            <div class="card p-4 border shadow-sm" style="background-color: #e3f2fd;">
                                <h6 class="text-uppercase"><strong>إجمالي المبلغ</strong></h6>
                                <p class="fs-4 mb-0 text-primary fw-bold"><?php echo e(number_format($invoice->total_amount_due, 2)); ?> جنيه</p>
                            </div>
                        </div>
                        <!-- Total Hours Worked -->
                        <div class="col-md-3">
                            <div class="card p-4 border shadow-sm" style="background-color: #f3e5f5;">
                                <h6 class="text-uppercase"><strong>إجمالي الساعات</strong></h6>
                                <?php
                                    $totalHours = $totalHoursWorked;
                                    $hours = floor($totalHours);
                                    $minutes = round(($totalHours - $hours) * 60);
                                    if ($minutes >= 60) {
                                        $hours += floor($minutes / 60);
                                        $minutes = $minutes % 60;
                                    }
                                    $formattedHours = convertToArabicNumbers($hours);
                                    $formattedMinutes = convertToArabicNumbers($minutes);
                                ?>
                                <p class="fs-4 mb-0 text-purple fw-bold"><?php echo e($formattedHours); ?> ساعة و <?php echo e($formattedMinutes); ?> دقيقة</p>
                            </div>
                        </div>
                        <!-- Hourly Rate -->
                        <div class="col-md-3">
                            <div class="card p-4 border shadow-sm" style="background-color: #e8f5e8;">
                                <h6 class="text-uppercase"><strong>سعر الساعة</strong></h6>
                                <p class="fs-4 mb-0 text-success fw-bold"><?php echo e(number_format($totalHoursWorked > 0 ? $invoice->total_amount_due / $totalHoursWorked : 0, 2)); ?> جنيه</p>
                            </div>
                        </div>
                        <!-- Number of Clients -->
                        <div class="col-md-3">
                            <div class="card p-4 border shadow-sm" style="background-color: #fff3e0;">
                                <h6 class="text-uppercase"><strong>عدد العملاء</strong></h6>
                                <p class="fs-4 mb-0 text-warning fw-bold"><?php echo e(count($clientData)); ?> عميل</p>
                            </div>
                        </div>
                    </div>
                    <div class="hstack gap-2 justify-content-end d-print-none mt-4">
                        <a href="javascript:window.print()" class="btn btn-success"><i class="ri-printer-line align-bottom me-1"></i> <?php echo app('translator')->get('client-invoice.print'); ?></a>
                    </div>
                    <!-- Back Button -->
                    <div class="d-flex justify-content-end mt-4">
                        <a href="<?php echo e(route('invoice-reason.index')); ?>" class="btn btn-secondary">
                            <i class="mdi mdi-arrow-left me-1"></i> <?php echo e(__('showInvoice.back_to_invoices')); ?>

                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script src="<?php echo e(URL::asset('/assets/libs/apexcharts/apexcharts.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('/assets/js/pages/dashboard-client-statistics-projects.init.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('/assets/js/app.min.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\Beer-Elshabab\resources\views/invoice-reason/show.blade.php ENDPATH**/ ?>