<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('client_balances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('cascade'); // للعملاء المشاركين
            $table->foreignId('invoice_reason_id')->constrained('invoice_reasons')->onDelete('cascade');
            $table->decimal('amount', 10, 2); // المبلغ (سالب)
            $table->enum('status', ['active', 'used', 'cancelled'])->default('active');
            $table->text('description')->nullable();
            $table->timestamps();
            
            // فهرس مركب لتجنب التكرار
            $table->unique(['client_id', 'customer_id', 'invoice_reason_id'], 'unique_client_balance');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('client_balances');
    }
};
