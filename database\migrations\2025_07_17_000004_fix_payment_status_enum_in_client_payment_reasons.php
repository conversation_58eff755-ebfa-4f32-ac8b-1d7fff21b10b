<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix the typo in payment_status enum: 'patial' -> 'partial'
        DB::statement("ALTER TABLE client_payment_reasons MODIFY COLUMN payment_status ENUM('unpaid', 'partial', 'paid') DEFAULT 'unpaid'");
        
        // Add missing columns
        Schema::table('client_payment_reasons', function (Blueprint $table) {
            $table->decimal('partial_payment', 10, 2)->default(0)->after('amount_due');
            $table->decimal('remaining_amount', 10, 2)->nullable()->after('partial_payment');
            $table->decimal('hours_worked', 8, 2)->default(0)->after('remaining_amount');
            $table->date('payment_date')->nullable()->after('hours_worked');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('client_payment_reasons', function (Blueprint $table) {
            $table->dropColumn(['partial_payment', 'remaining_amount', 'hours_worked', 'payment_date']);
        });
        
        // Revert the enum back to original (with typo)
        DB::statement("ALTER TABLE client_payment_reasons MODIFY COLUMN payment_status ENUM('unpaid', 'patial', 'paid') DEFAULT 'unpaid'");
    }
};
