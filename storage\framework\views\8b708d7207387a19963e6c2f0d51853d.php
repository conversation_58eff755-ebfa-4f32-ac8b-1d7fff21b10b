<?php $__env->startSection('title'); ?> تحديث حالة الدفع <?php $__env->stopSection(); ?>

<?php $__env->startSection('css'); ?>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php $__env->startComponent('components.breadcrumb'); ?>
        <?php $__env->slot('li_1'); ?> النماذج <?php $__env->endSlot(); ?>
        <?php $__env->slot('title'); ?> تحديث حالة الدفع <?php $__env->endSlot(); ?>
    <?php echo $__env->renderComponent(); ?>
<div class="container mt-5">
    <h1 class="mb-4 text-center">تحديث حالة دفع فواتير الأسباب</h1>
    <div class="card">
        <div class="card-body">
    <?php if(session('success')): ?>
        <div class="alert alert-success">
            <?php echo e(session('success')); ?>

        </div>
    <?php endif; ?>

    <form action="<?php echo e(route('invoice-reason.update-payment-status')); ?>" method="POST">
        <?php echo csrf_field(); ?>

        <!-- Time Period Selection -->
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
            <label for="period">اختر الفترة الزمنية</label>
            <select name="period" class="form-select" id="period" required>
                <?php if($timePeriods->isNotEmpty()): ?>
                    <?php $__currentLoopData = $timePeriods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $period): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($period); ?>" <?php echo e($selectedPeriod == $period ? 'selected' : ''); ?>>
                            <?php echo e($period); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <option disabled>لا توجد فترات متاحة</option>
                <?php endif; ?>
            </select>
        </div>
        </div>

        <!-- Select Clients -->
        <div class="col-md-6">
            <div class="mb-3">
            <label for="clients">اختر العملاء</label>
            <select name="client_ids[]" class="form-control" id="client-select" multiple="multiple" required>
                <option value="all">اختر الكل</option>
                <?php $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($client->id); ?>"><?php echo e($client->name); ?></option>
                    <?php $__currentLoopData = $client->customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($client->id); ?>_<?php echo e($customer->id); ?>" data-client-id="<?php echo e($client->id); ?>" data-customer-id="<?php echo e($customer->id); ?>">
                            &nbsp;&nbsp;&nbsp;<?php echo e($customer->name); ?> (عميل مشارك - <?php echo e($client->name); ?>)
                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
        </div>

        <!-- Payment Status -->
        <div class="col-lg-6">
            <div class="mb-3">
            <label for="payment_status">حالة الدفع</label>
            <select name="payment_status" id="payment_status" class="form-control" required>
                <option value="paid">مدفوع</option>
                <option value="partial">دفع جزئي</option>
                <option value="unpaid">غير مدفوع</option>
            </select>
        </div>
        </div>

        <!-- Partial Payment Fields -->
        <div class="col-lg-6" id="partial-payment-fields" style="display:none;">
            <label for="partial_payment">مبلغ الدفع الجزئي</label>
            <input type="number" name="partial_payment" id="partial_payment" class="form-control" placeholder="أدخل مبلغ الدفع الجزئي" step="0.01">
        </div>

        <!-- Remaining Amount -->
        <div class="col-lg-6" id="remaining-amount-fields" style="display:none;">
            <div class="mb-4">
            <label for="remaining_amount">المبلغ المتبقي</label>
            <input type="number" name="remaining_amount" id="remaining_amount" class="form-control" placeholder="المبلغ المتبقي" disabled>
            </div>
        </div>

        <div class="col-lg-6">
            <label for="payment_date">تاريخ الدفع</label>
            <input type="date" name="payment_date" id="payment_date" class="form-control" required>
        </div>

        <div class="row">
            <div class="d-flex justify-content-end mt-4">
                <div class="mb-4">
                    <button type="submit" class="btn btn-primary btn-block">تحديث حالة الدفع</button>
                </div>
            </div>
        </div>
    </div>
        </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
<script src="<?php echo e(URL::asset('/assets/js/app.min.js')); ?>"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize Select2
    $('#client-select').select2({
        placeholder: "اختر العملاء",
        allowClear: true
    });

    // Function to handle Select All functionality
    $('#client-select').on('change', function() {
        var selectedOptions = $(this).val();

        if (selectedOptions && selectedOptions.includes('all')) {
            var allClientIds = $('#client-select option[value!="all"]').map(function() {
                return $(this).val();
            }).get();
            $('#client-select').val(allClientIds).trigger('change');
        }
    });

    // Function to fetch amount due when clients are selected/changed
    function fetchAmountDue() {
        let period = $('#period').val();
        let clients = $('#client-select').val();

        if (period && clients.length) {
            $.ajax({
                url: '<?php echo e(route("invoice-reason.get-amount-due")); ?>',
                method: 'POST',
                data: {
                    period: period,
                    client_ids: clients,
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                success: function(response) {
                    $('#remaining_amount').val(response.amount_due); // Show the amount due
                },
                error: function(xhr) {
                    console.log('Error fetching amount due:', xhr);
                }
            });
        }
    }

    // Fetch amount due when the period or client selection changes
    $('#period').on('change', fetchAmountDue);
    $('#client-select').on('change', fetchAmountDue);

    // Show or hide partial payment fields based on the selected payment status
    $('#payment_status').on('change', function() {
        var paymentStatus = $(this).val();
        if (paymentStatus === 'partial') {
            $('#partial-payment-fields').show();
            $('#remaining-amount-fields').show();
            fetchAmountDue(); // Fetch amount due when partial payment is selected
        } else {
            $('#partial-payment-fields').hide();
            $('#remaining-amount-fields').hide();
            $('#remaining_amount').val(0); // Reset remaining amount if not partial
        }
    });

    // Adjust remaining amount when partial payment is entered
    $('#partial_payment').on('input', function() {
        var partialPayment = parseFloat($(this).val()) || 0;
        var totalDue = parseFloat($('#remaining_amount').val()) || 0;
        var remainingAmount = totalDue - partialPayment;

        // Avoid negative amounts
        var finalRemainingAmount = Math.max(remainingAmount, 0);
        $('#remaining_amount').val(finalRemainingAmount.toFixed(2));
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\Beer-Elshabab\resources\views/invoice-reason/update-payment-status.blade.php ENDPATH**/ ?>