<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_reasons', function (Blueprint $table) {
            $table->json('wells_config')->nullable()->after('end_date');
            $table->json('wells_hourly_rates')->nullable()->after('wells_config');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_reasons', function (Blueprint $table) {
            $table->dropColumn(['wells_config', 'wells_hourly_rates']);
        });
    }
};
