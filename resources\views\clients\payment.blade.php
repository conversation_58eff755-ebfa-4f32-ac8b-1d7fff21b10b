@extends('layouts.master')

@section('title') @lang('update_payment_status.update_payment_statu') @endsection

@section('css')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
@endsection

@section('content')
    @component('components.breadcrumb')
        @slot('li_1') Forms @endslot
        @slot('title') @lang('update_payment_status.update_payment_statu') @endslot
    @endcomponent

<div class="container mt-5">
    <div class="card">
        <div class="card-body">
            <h1 class="mb-4 text-center">@lang('update_payment_status.update_payment_status')</h1>

            @if(session('success'))
                <div class="alert alert-success">
                    @lang('update_payment_status.success')
                </div>
            @endif
            @if(session('warning'))
            <div class="alert alert-warning">
                {{ session('warning') }}
            </div>
        @endif
            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form action="{{ route('payment-status.update') }}" method="POST" id="payment-form">
                @csrf

                <!-- Invoice Month -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="invoice_month" class="form-label">@lang('update_payment_status.select_invoice_month')</label>
                            <select name="invoice_month" id="invoice_month" class="form-control" required>
                                @foreach($months as $month)
                                    @php
                                        $monthCarbon = \Carbon\Carbon::parse($month);
                                        $formattedMonth = app()->getLocale() == 'ae'
                                            ? $monthCarbon->locale('ar')->translatedFormat('F Y')
                                            : $monthCarbon->format('F Y');
                                    @endphp
                                    <option value="{{ $month }}" {{ old('invoice_month') == $month ? 'selected' : '' }}>
                                        {{ $formattedMonth }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <!-- Clients -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="clients" class="form-label">@lang('update_payment_status.select_clients')</label>
                            <select name="client_ids[]" id="client-select" class="form-control" multiple="multiple" required>
                                <option value="all">@lang('update_payment_status.select_all')</option>
                                @foreach($clients as $client)
                                    <option value="{{ $client->id }}">{{ $client->name }}</option>
                                    @foreach($client->customers as $customer)
                                        <option value="{{ $client->id }}_{{ $customer->id }}" data-client-id="{{ $client->id }}" data-customer-id="{{ $customer->id }}">
                                            &nbsp;&nbsp;&nbsp;{{ $customer->name }} (عميل مشارك - {{ $client->name }})
                                        </option>
                                    @endforeach
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <!-- Payment Status -->
                    <div class="col-lg-6">
                        <div class="mb-3">
                            <label for="payment_status" class="form-label">@lang('update_payment_status.payment_status')</label>
                            <select name="payment_status" id="payment_status" class="form-control" required>
                                <option value="paid">@lang('update_payment_status.paid')</option>
                                <option value="partial">@lang('update_payment_status.partial_payment')</option>
                                <option value="unpaid">@lang('update_payment_status.unpaid')</option>
                            </select>
                        </div>
                    </div>

                    <!-- Partial Payment Fields -->
                    <div class="col-lg-6" id="partial-payment-fields" style="display:none;">
                            <label for="partial_payment" class="form-label">@lang('update_payment_status.partial_payment_amount')</label>
                            <input type="number" name="partial_payment" id="partial_payment" class="form-control" placeholder="@lang('update_payment_status.partial_payment_amount')" step="0.01">
                    </div>

                    <!-- Current Remaining Amount Display -->
                    <div class="col-lg-12" id="current-remaining-display" style="display:none;">
                        <div class="alert alert-info mb-3">
                            <strong>المبلغ المتبقي الحالي:</strong> <span id="current-remaining-text">0</span> جنيه
                            <br><small>هذا هو المبلغ المتبقي من الفاتورة (شامل رسوم الصيانة والتشغيل)</small>
                        </div>
                    </div>

                    <!-- Remaining Amount Fields -->
                    <div class="col-lg-6" id="remaining-amount-fields" style="display:none;">
                            <div class="mb-3">
                                <label for="remaining_amount" class="form-label">المبلغ المتبقي بعد هذا الدفع</label>
                                <input type="number" name="remaining_amount" id="remaining_amount" class="form-control" placeholder="المبلغ المتبقي بعد الدفع" disabled>
                                <small class="text-muted">هذا المبلغ سيتبقى بعد الدفع الجزئي</small>
                            </div>
                    </div>

                    <!-- Payment Date -->
                    <div class="col-lg-6">
                        <div class="mb-3">
                            <label for="payment_date" class="form-label">@lang('update_payment_status.payment_date')</label>
                            <input type="date" name="payment_date" id="payment_date" class="form-control" required>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="row">
                    <div class="d-flex justify-content-end mt-4">
                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary btn-block">@lang('update_payment_status.submit')</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('script')
<script src="{{ URL::asset('/assets/js/app.min.js') }}"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    $('#client-select').select2({
        placeholder: "@lang('update_payment_status.select_clients')",
        allowClear: true
    });



    $('#client-select').on('change', function() {
        var selectedOptions = $(this).val();
        if (selectedOptions && selectedOptions.includes('all')) {
            var allClientIds = $('#client-select option[value!="all"]').map(function() {
                return $(this).val();
            }).get();
            $('#client-select').val(allClientIds).trigger('change');
        }

        // Update amount due when client selection changes
        fetchAmountDue();
    });

    function fetchAmountDue() {
        let month = $('#invoice_month').val();
        let clients = $('#client-select').val();
        if (month && clients.length) {
            $.ajax({
                url: '{{ route("get.amount.due") }}',
                method: 'GET',
                data: { invoice_month: month, client_ids: clients },
                success: function(response) {
                    console.log('Response from server:', response);
                    console.log('remaining_amount:', response.remaining_amount);
                    console.log('amount_due:', response.amount_due);
                    var currentRemaining = response.remaining_amount || response.amount_due;
                    var totalAmount = response.amount_due;

                    // عرض المبلغ المتبقي الفعلي (بعد خصم أي دفعات سابقة)
                    $('#remaining_amount').val(currentRemaining.toFixed(2));
                    $('#current-remaining-text').text(currentRemaining.toFixed(2));

                    // حفظ المبلغ الإجمالي للتحقق من صحة الإدخال
                    $('#remaining_amount').data('total-amount', totalAmount);
                    $('#remaining_amount').data('current-remaining', currentRemaining);

                    // If all clients are paid, show a message
                    if (currentRemaining <= 0 && response.client_statuses && response.client_statuses.length > 0) {
                        // Check if all selected clients are fully paid
                        var allPaid = response.client_statuses.every(function(client) {
                            return client.is_fully_paid || client.remaining_amount <= 0;
                        });

                        if (allPaid) {
                            $('#current-remaining-display .alert').removeClass('alert-info').addClass('alert-success');
                            $('#current-remaining-display .alert strong').text('جميع العملاء المختارين مدفوعين بالكامل ✅');
                            $('#current-remaining-display .alert small').text('لا يوجد مبلغ متبقي للدفع');
                        } else {
                            $('#current-remaining-display .alert').removeClass('alert-success').addClass('alert-info');
                            $('#current-remaining-display .alert strong').text('المبلغ المتبقي الحالي:');
                            $('#current-remaining-display .alert small').text('هذا هو المبلغ المتبقي من الفاتورة (شامل رسوم الصيانة والتشغيل)');
                        }
                    } else {
                        $('#current-remaining-display .alert').removeClass('alert-success').addClass('alert-info');
                        $('#current-remaining-display .alert strong').text('المبلغ المتبقي الحالي:');
                        $('#current-remaining-display .alert small').text('هذا هو المبلغ المتبقي من الفاتورة (شامل رسوم الصيانة والتشغيل)');
                    }


                },
                error: function(xhr) {
                    console.log('Error fetching amount due:', xhr);
                }
            });
        }
    }

    $('#invoice_month, #client-select').on('change', fetchAmountDue);

    $('#payment_status').on('change', function() {
        var paymentStatus = $(this).val();
        if (paymentStatus === 'partial') {
            $('#partial-payment-fields').show();
            $('#remaining-amount-fields').show();
            $('#current-remaining-display').show();
            fetchAmountDue();
        } else {
            $('#partial-payment-fields').hide();
            $('#remaining-amount-fields').hide();
            $('#current-remaining-display').hide();
        }
    });

    $('#partial_payment').on('input', function() {
        var partialPayment = parseFloat($(this).val()) || 0;
        var currentRemaining = parseFloat($('#remaining_amount').data('current-remaining')) || 0;
        var totalAmount = parseFloat($('#remaining_amount').data('total-amount')) || 0;

        // إزالة أي رسائل خطأ سابقة
        $('.payment-error').remove();
        $(this).removeClass('is-invalid');

        // التحقق من أن المبلغ المدخل لا يتجاوز المبلغ المتبقي
        if (partialPayment > currentRemaining) {
            $(this).addClass('is-invalid');
            $(this).after('<div class="invalid-feedback payment-error">المبلغ المدخل (' + partialPayment.toFixed(2) + ' جنيه) أكبر من المبلغ المتبقي (' + currentRemaining.toFixed(2) + ' جنيه)</div>');
            $('#remaining_amount').val(0);
            return;
        }

        // حساب المبلغ المتبقي الجديد
        var newRemainingAmount = currentRemaining - partialPayment;
        $('#remaining_amount').val(Math.max(newRemainingAmount, 0).toFixed(2));
    });

    // التحقق عند إرسال النموذج
    $('#payment-form').on('submit', function(e) {
        var paymentStatus = $('#payment_status').val();
        var selectedClients = $('#client-select').val();
        var selectedMonth = $('#invoice_month').val();

        // Check if month is selected
        if (!selectedMonth) {
            e.preventDefault();
            alert('يرجى اختيار شهر الفاتورة أولاً');
            return false;
        }

        // Check if clients are selected
        if (!selectedClients || selectedClients.length === 0) {
            e.preventDefault();
            alert('يرجى اختيار عميل واحد على الأقل');
            return false;
        }

        if (paymentStatus === 'partial') {
            var partialPayment = parseFloat($('#partial_payment').val()) || 0;
            var currentRemaining = parseFloat($('#remaining_amount').data('current-remaining')) || 0;

            if (partialPayment <= 0) {
                e.preventDefault();
                alert('يرجى إدخال مبلغ دفع جزئي صحيح');
                return false;
            }

            if (partialPayment > currentRemaining) {
                e.preventDefault();
                alert('المبلغ المدخل (' + partialPayment.toFixed(2) + ' جنيه) أكبر من المبلغ المتبقي (' + currentRemaining.toFixed(2) + ' جنيه)');
                return false;
            }
        }
    });
});
</script>
@endsection
