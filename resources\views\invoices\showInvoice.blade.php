@extends('layouts.master')

@section('content')
    @component('components.breadcrumb')
        @slot('li_1')
            @lang('showInvoice.management')
        @endslot
        @slot('title')
            @lang('showInvoice.invoice_details')
        @endslot
    @endcomponent

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <!-- Success and Error Messages -->
                    @if(session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @elseif(session('error'))
                        <div class="alert alert-danger">{{ session('error') }}</div>
                    @endif

                    <!-- Invoice Summary -->
                    <div class="invoice-summary mb-4 text-center">
                        <h4 class="card-title mb-3"><strong>@lang('showInvoice.invoice_summary')</strong></h4>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="fs-5"><strong>@lang('showInvoice.total_amount_due') @formatNumber($invoice->total_amount_due) {{ __('showInvoice.currency') }}</strong></p>
                            </div>
                            <div class="col-md-6">
                                @php
                                $monthCarbon = \Carbon\Carbon::parse($invoice->month);
                                $formattedMonth = app()->getLocale() == 'ae'
                                    ? $monthCarbon->locale('ar')->translatedFormat('F Y')
                                    : $monthCarbon->format('F Y');
                                @endphp
                                <p class="fs-5"><strong>@lang('showInvoice.month'): {{ $formattedMonth }}</strong></p>
                            </div>
                            @foreach($invoice->wells as $well)
                            <div class="col-md-6">
                                <p class="fs-5"><strong>{{ $well['name'] }}: @formatNumber($well->pivot->hour_price) {{ __('showInvoice.currency') }} </strong></p>
                            </div>
                            @endforeach
                        </div>
                    </div>

                    <form method="GET" action="{{ route('invoices.showInvoice', $invoice->id) }}" class="mb-4">
                        <div class="row justify-content-center align-items-center text-center">
                            <!-- العنوان والفلتر -->
                            <div class="col-md-6">
                                <label for="filter_status" class="form-label fw-bold text-primary">
                                    @lang('showInvoice.filter')
                                </label>
                                <div class="input-group justify-content-center">
                                    <select
                                        name="filter_status"
                                        id="filter_status"
                                        class="form-select border-primary shadow-sm text-center"
                                        onchange="this.form.submit()">
                                        <option value="">@lang('showInvoice.all')</option>
                                        <option value="paid" {{ request('filter_status') == 'paid' ? 'selected' : '' }}>
                                            @lang('showInvoice.paid')
                                        </option>
                                        <option value="unpaid" {{ request('filter_status') == 'unpaid' ? 'selected' : '' }}>
                                            @lang('showInvoice.unpaid')
                                        </option>
                                        <option value="has_due" {{ request('filter_status') == 'has_due' ? 'selected' : '' }}>
                                            @lang('showInvoice.has_due')
                                        </option>
                                        <option value="no_due" {{ request('filter_status') == 'no_due' ? 'selected' : '' }}>
                                            @lang('showInvoice.no_due')
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Client Breakdown Table -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">@lang('showInvoice.client_breakdown')</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover align-middle table-bordered mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>@lang('showInvoice.id')</th>
                                            <th>@lang('showInvoice.client_name')</th>
                                            <th>@lang('showInvoice.amount_due')</th>
                                            <th>@lang('showInvoice.additional_due')</th>
                                            <th>@lang('showInvoice.maintenance_and_operation_fees')</th>
                                            <th>@lang('showInvoice.payment_status')</th>
                                            <th>@lang('showInvoice.remaining_amount')</th>
                                            <th class="text-center">@lang('showInvoice.hours_worked')</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if(empty($separatedPayments))
                                            <tr>
                                                <td colspan="8" class="text-center">@lang('showInvoice.no_records')</td>
                                            </tr>
                                        @else
                                            @foreach($separatedPayments as $payment)
                                                <tr>
                                                    <td><strong>{{ $payment['client_id'] }}</strong></td>
                                                    <td>
                                                        <strong>{{ $payment['client_name'] }}</strong>
                                                        @if($payment['type'] == 'customer')
                                                            <br><small class="text-muted">{{ $payment['customer_name'] }} (عميل مشارك)</small>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <strong>@formatNumber($payment['amount_due']) {{ __('showInvoice.currency') }}</strong>
                                                    </td>
                                                    <td><strong>@formatNumber($payment['additional_paid']) {{ __('showInvoice.currency') }}</strong></td>
                                                    <td class="text-center"><strong>@formatNumber($payment['active_bonus']) {{ __('showInvoice.currency') }}</strong></td>
                                                    <td><strong>
                                                        @if($payment['payment_status'] == 'paid')
                                                            <span class="badge bg-success">@lang('showInvoice.paid')</span>
                                                        @elseif($payment['payment_status'] == 'partial')
                                                            <span class="badge bg-warning">@lang('showInvoice.partial_payment')</span>
                                                        @else
                                                            <span class="badge bg-danger">@lang('showInvoice.unpaid')</span>
                                                        @endif
                                                    </strong></td>
                                                    <td><strong>@formatNumber($payment['remaining_amount']) {{ __('showInvoice.currency') }}</strong></td>
                                                    <td>
                                                        @if($payment['type'] == 'customer')
                                                            <span class="text-muted">ساعات العميل المشارك</span>
                                                        @else
                                                            <span class="text-muted">ساعات العميل الأساسي</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- جدول تفاصيل الساعات (الجدول القديم للمرجع) -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">تفاصيل الساعات</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover align-middle table-bordered mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>@lang('showInvoice.id')</th>
                                            <th>@lang('showInvoice.client_name')</th>
                                            <th class="text-center">@lang('showInvoice.hours_worked')</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if(empty($combinedWorkData))
                                            <tr>
                                                <td colspan="3" class="text-center">@lang('showInvoice.no_records')</td>
                                            </tr>
                                        @else
                                            @foreach($combinedWorkData as $clientId => $data)
                                                <tr>
                                                    <td><strong>{{ $clientId }}</strong></td>
                                                    <td>
                                                        <strong>{{ $data['client_name'] }}</strong>
                                                        @if(!empty($data['customer_data']) && $data['customer_hours'] > 0)
                                                            <ul style="list-style: none; padding-left: 0; margin: 0;">
                                                                @foreach($data['customer_data'] as $customer)
                                                                    @if($customer['hours'] > 0)
                                                                        <li><strong>{{ $customer['name'] }}</strong></li>
                                                                    @endif
                                                                @endforeach
                                                            </ul>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <ul>
                                                            @if($data['client_hours'] > 0)
                                                                @foreach($data['client_wells'] as $well)
                                                                    @php
                                                                        $totalHoursWorked = $well['hours'];
                                                                        $totalHours = floor($totalHoursWorked);
                                                                        $minutes = round(($totalHoursWorked - $totalHours) * 60);
                                                                        if ($minutes >= 60) {
                                                                            $totalHours += floor($minutes / 60);
                                                                            $minutes = $minutes % 60;
                                                                        }
                                                                        $locale = app()->getLocale();
                                                                        if ($locale === 'ae') {
                                                                            $formattedHours = convertToArabicNumbers($totalHours);
                                                                            $formattedMinutes = convertToArabicNumbers($minutes);
                                                                            $hoursLabel = trans('hours.hours');
                                                                            $minutesLabel = trans('hours.minutes');
                                                                        } else {
                                                                            $formattedHours = $totalHours;
                                                                            $formattedMinutes = $minutes;
                                                                            $hoursLabel = trans('hours.hours');
                                                                            $minutesLabel = trans('hours.minutes');
                                                                        }
                                                                    @endphp
                                                                    <div>
                                                                    <strong>{{ $well['name'] }}: {{ $formattedHours }} {{ $hoursLabel }}, {{ $formattedMinutes }} {{ $minutesLabel }}</strong></div>
                                                                @endforeach
                                                            @endif
                                                            @if($data['customer_hours'] > 0)
                                                                @foreach($data['customer_data'] as $customer)
                                                                    @if($customer['hours'] > 0)
                                                                        @foreach($customer['wells'] as $well)
                                                                            @php
                                                                                $totalHoursWorked = $well['hours'];
                                                                                $totalHours = floor($totalHoursWorked);
                                                                                $minutes = round(($totalHoursWorked - $totalHours) * 60);
                                                                                if ($minutes >= 60) {
                                                                                    $totalHours += floor($minutes / 60);
                                                                                    $minutes = $minutes % 60;
                                                                                }
                                                                                $locale = app()->getLocale();
                                                                                if ($locale === 'ae') {
                                                                                    $formattedHours = convertToArabicNumbers($totalHours);
                                                                                    $formattedMinutes = convertToArabicNumbers($minutes);
                                                                                    $hoursLabel = trans('hours.hours');
                                                                                    $minutesLabel = trans('hours.minutes');
                                                                                } else {
                                                                                    $formattedHours = $totalHours;
                                                                                    $formattedMinutes = $minutes;
                                                                                    $hoursLabel = trans('hours.hours');
                                                                                    $minutesLabel = trans('hours.minutes');
                                                                                }
                                                                            @endphp
                                                                            <div>
                                                                                <strong>{{ $customer['name'] }} - {{ $well['name'] }}: {{ $formattedHours }} {{ $hoursLabel }}, {{ $formattedMinutes }} {{ $minutesLabel }}</strong>
                                                                            </div>
                                                                        @endforeach
                                                                    @endif
                                                                @endforeach
                                                            @endif
                                                        </ul>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Total Remaining Amount Box -->
                    <div class="card mt-4">
                        <div class="card-body text-center">
                            <h5 class="card-title mb-3">@lang('showInvoice.total_remaining_amount')</h5>
                            @php
                                $totalRemaining = 0;
                                if (!empty($separatedPayments)) {
                                    foreach ($separatedPayments as $payment) {
                                        $totalRemaining += $payment['remaining_amount'];
                                    }
                                }
                            @endphp
                            <p class="fs-4"><strong>@formatNumber($totalRemaining) {{ __('showInvoice.currency') }}</strong></p>
                        </div>
                    </div>

                    <div class="hstack gap-2 justify-content-end d-print-none mt-4">
                        <a href="javascript:window.print()" class="btn btn-success"><i class="ri-printer-line align-bottom me-1"></i> @lang('client-invoice.print')</a>
                    </div>
                    <!-- Back Button -->
                    <div class="d-flex justify-content-end mt-4">
                        <a href="{{ route('invoices.index') }}" class="btn btn-secondary">
                            <i class="mdi mdi-arrow-left me-1"></i> @lang('showInvoice.back_to_invoices')
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script src="{{ URL::asset('/assets/libs/apexcharts/apexcharts.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/js/pages/dashboard-client-statistics-projects.init.js') }}"></script>
    <script src="{{ URL::asset('/assets/js/app.min.js') }}"></script>
@endsection
