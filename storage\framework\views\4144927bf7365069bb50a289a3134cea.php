<?php $__env->startSection('content'); ?>
    <?php $__env->startComponent('components.breadcrumb'); ?>
        <?php $__env->slot('li_1'); ?>
            <?php echo app('translator')->get('showInvoice.management'); ?>
        <?php $__env->endSlot(); ?>
        <?php $__env->slot('title'); ?>
            <?php echo app('translator')->get('showInvoice.invoice_details'); ?>
        <?php $__env->endSlot(); ?>
    <?php echo $__env->renderComponent(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <!-- Success and Error Messages -->
                    <?php if(session('success')): ?>
                        <div class="alert alert-success"><?php echo e(session('success')); ?></div>
                    <?php elseif(session('error')): ?>
                        <div class="alert alert-danger"><?php echo e(session('error')); ?></div>
                    <?php endif; ?>

                    <!-- Invoice Summary -->
                    <div class="invoice-summary mb-4 text-center">
                        <h4 class="card-title mb-3"><strong><?php echo app('translator')->get('showInvoice.invoice_summary'); ?></strong></h4>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="fs-5"><strong><?php echo app('translator')->get('showInvoice.total_amount_due'); ?> <?php
                if (app()->getLocale() == 'ae') {
                    echo convertToArabicNumbers(number_format($invoice->total_amount_due, 2));
                } else {
                    echo number_format($invoice->total_amount_due, 2);
                }
            ?> <?php echo e(__('showInvoice.currency')); ?></strong></p>
                            </div>
                            <div class="col-md-6">
                                <?php
                                $monthCarbon = \Carbon\Carbon::parse($invoice->month);
                                $formattedMonth = app()->getLocale() == 'ae'
                                    ? $monthCarbon->locale('ar')->translatedFormat('F Y')
                                    : $monthCarbon->format('F Y');
                                ?>
                                <p class="fs-5"><strong><?php echo app('translator')->get('showInvoice.month'); ?>: <?php echo e($formattedMonth); ?></strong></p>
                            </div>
                            <?php $__currentLoopData = $invoice->wells; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $well): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-6">
                                <p class="fs-5"><strong><?php echo e($well['name']); ?>: <?php
                if (app()->getLocale() == 'ae') {
                    echo convertToArabicNumbers(number_format($well->pivot->hour_price, 2));
                } else {
                    echo number_format($well->pivot->hour_price, 2);
                }
            ?> <?php echo e(__('showInvoice.currency')); ?> </strong></p>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>

                    <form method="GET" action="<?php echo e(route('invoices.showInvoice', $invoice->id)); ?>" class="mb-4">
                        <div class="row justify-content-center align-items-center text-center">
                            <!-- العنوان والفلتر -->
                            <div class="col-md-6">
                                <label for="filter_status" class="form-label fw-bold text-primary">
                                    <?php echo app('translator')->get('showInvoice.filter'); ?>
                                </label>
                                <div class="input-group justify-content-center">
                                    <select
                                        name="filter_status"
                                        id="filter_status"
                                        class="form-select border-primary shadow-sm text-center"
                                        onchange="this.form.submit()">
                                        <option value=""><?php echo app('translator')->get('showInvoice.all'); ?></option>
                                        <option value="paid" <?php echo e(request('filter_status') == 'paid' ? 'selected' : ''); ?>>
                                            <?php echo app('translator')->get('showInvoice.paid'); ?>
                                        </option>
                                        <option value="unpaid" <?php echo e(request('filter_status') == 'unpaid' ? 'selected' : ''); ?>>
                                            <?php echo app('translator')->get('showInvoice.unpaid'); ?>
                                        </option>
                                        <option value="has_due" <?php echo e(request('filter_status') == 'has_due' ? 'selected' : ''); ?>>
                                            <?php echo app('translator')->get('showInvoice.has_due'); ?>
                                        </option>
                                        <option value="no_due" <?php echo e(request('filter_status') == 'no_due' ? 'selected' : ''); ?>>
                                            <?php echo app('translator')->get('showInvoice.no_due'); ?>
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Client Breakdown Table -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><?php echo app('translator')->get('showInvoice.client_breakdown'); ?></h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover align-middle table-bordered mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th><?php echo app('translator')->get('showInvoice.id'); ?></th>
                                            <th><?php echo app('translator')->get('showInvoice.client_name'); ?></th>
                                            <th><?php echo app('translator')->get('showInvoice.amount_due'); ?></th>
                                            <th><?php echo app('translator')->get('showInvoice.additional_due'); ?></th>
                                            <th><?php echo app('translator')->get('showInvoice.maintenance_and_operation_fees'); ?></th>
                                            <th><?php echo app('translator')->get('showInvoice.payment_status'); ?></th>
                                            <th><?php echo app('translator')->get('showInvoice.remaining_amount'); ?></th>
                                            <th class="text-center"><?php echo app('translator')->get('showInvoice.hours_worked'); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if(empty($separatedPayments)): ?>
                                            <tr>
                                                <td colspan="8" class="text-center"><?php echo app('translator')->get('showInvoice.no_records'); ?></td>
                                            </tr>
                                        <?php else: ?>
                                            <?php
                                                $groupedPayments = [];
                                                foreach($separatedPayments as $payment) {
                                                    $clientId = $payment['client_id'];
                                                    if (!isset($groupedPayments[$clientId])) {
                                                        $groupedPayments[$clientId] = [
                                                            'main' => null,
                                                            'customers' => []
                                                        ];
                                                    }

                                                    if ($payment['type'] == 'main_client') {
                                                        $groupedPayments[$clientId]['main'] = $payment;
                                                    } else {
                                                        $groupedPayments[$clientId]['customers'][] = $payment;
                                                    }
                                                }
                                            ?>

                                            <?php $__currentLoopData = $groupedPayments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $clientId => $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php if($group['main']): ?>
                                                    <tr>
                                                        <td><strong><?php echo e($clientId); ?></strong></td>
                                                        <td>
                                                            <strong><?php echo e($group['main']['client_name']); ?></strong>
                                                            <?php if(!empty($group['customers'])): ?>
                                                                <ul style="list-style: none; padding-left: 0; margin: 0;">
                                                                    <?php $__currentLoopData = $group['customers']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <li><strong><?php echo e($customer['customer_name']); ?></strong></li>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </ul>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <strong><?php
                if (app()->getLocale() == 'ae') {
                    echo convertToArabicNumbers(number_format($group['main']['amount_due'], 2));
                } else {
                    echo number_format($group['main']['amount_due'], 2);
                }
            ?> <?php echo e(__('showInvoice.currency')); ?></strong>
                                                            <?php if(!empty($group['customers'])): ?>
                                                                <ul style="list-style: none; padding-left: 0; margin: 0;">
                                                                    <?php $__currentLoopData = $group['customers']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <li><strong><?php
                if (app()->getLocale() == 'ae') {
                    echo convertToArabicNumbers(number_format($customer['amount_due'], 2));
                } else {
                    echo number_format($customer['amount_due'], 2);
                }
            ?> <?php echo e(__('showInvoice.currency')); ?></strong></li>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </ul>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <strong><?php
                if (app()->getLocale() == 'ae') {
                    echo convertToArabicNumbers(number_format($group['main']['additional_paid'], 2));
                } else {
                    echo number_format($group['main']['additional_paid'], 2);
                }
            ?> <?php echo e(__('showInvoice.currency')); ?></strong>
                                                            <?php if(!empty($group['customers'])): ?>
                                                                <ul style="list-style: none; padding-left: 0; margin: 0;">
                                                                    <?php $__currentLoopData = $group['customers']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <li><strong><?php
                if (app()->getLocale() == 'ae') {
                    echo convertToArabicNumbers(number_format($customer['additional_paid'], 2));
                } else {
                    echo number_format($customer['additional_paid'], 2);
                }
            ?> <?php echo e(__('showInvoice.currency')); ?></strong></li>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </ul>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td class="text-center">
                                                            <strong><?php
                if (app()->getLocale() == 'ae') {
                    echo convertToArabicNumbers(number_format($group['main']['active_bonus'], 2));
                } else {
                    echo number_format($group['main']['active_bonus'], 2);
                }
            ?> <?php echo e(__('showInvoice.currency')); ?></strong>
                                                            <?php if(!empty($group['customers'])): ?>
                                                                <ul style="list-style: none; padding-left: 0; margin: 0;">
                                                                    <?php $__currentLoopData = $group['customers']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <li><strong><?php
                if (app()->getLocale() == 'ae') {
                    echo convertToArabicNumbers(number_format($customer['active_bonus'], 2));
                } else {
                    echo number_format($customer['active_bonus'], 2);
                }
            ?> <?php echo e(__('showInvoice.currency')); ?></strong></li>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </ul>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td><strong>
                                                            <?php if($group['main']['payment_status'] == 'paid'): ?>
                                                                <span class="badge bg-success"><?php echo app('translator')->get('showInvoice.paid'); ?></span>
                                                            <?php elseif($group['main']['payment_status'] == 'partial'): ?>
                                                                <span class="badge bg-warning"><?php echo app('translator')->get('showInvoice.partial_payment'); ?></span>
                                                            <?php else: ?>
                                                                <span class="badge bg-danger"><?php echo app('translator')->get('showInvoice.unpaid'); ?></span>
                                                            <?php endif; ?>
                                                        </strong></td>
                                                        <td>
                                                            <strong><?php
                if (app()->getLocale() == 'ae') {
                    echo convertToArabicNumbers(number_format($group['main']['remaining_amount'], 2));
                } else {
                    echo number_format($group['main']['remaining_amount'], 2);
                }
            ?> <?php echo e(__('showInvoice.currency')); ?></strong>
                                                            <?php if(!empty($group['customers'])): ?>
                                                                <ul style="list-style: none; padding-left: 0; margin: 0;">
                                                                    <?php $__currentLoopData = $group['customers']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <li><strong><?php
                if (app()->getLocale() == 'ae') {
                    echo convertToArabicNumbers(number_format($customer['remaining_amount'], 2));
                } else {
                    echo number_format($customer['remaining_amount'], 2);
                }
            ?> <?php echo e(__('showInvoice.currency')); ?></strong></li>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </ul>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <span class="text-muted">ساعات العميل الأساسي</span>
                                                            <?php if(!empty($group['customers'])): ?>
                                                                <ul style="list-style: none; padding-left: 0; margin: 0;">
                                                                    <?php $__currentLoopData = $group['customers']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <li><span class="text-muted">ساعات <?php echo e($customer['customer_name']); ?></span></li>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </ul>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endif; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- جدول تفاصيل الساعات (الجدول القديم للمرجع) -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">تفاصيل الساعات</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover align-middle table-bordered mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th><?php echo app('translator')->get('showInvoice.id'); ?></th>
                                            <th><?php echo app('translator')->get('showInvoice.client_name'); ?></th>
                                            <th class="text-center"><?php echo app('translator')->get('showInvoice.hours_worked'); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if(empty($combinedWorkData)): ?>
                                            <tr>
                                                <td colspan="3" class="text-center"><?php echo app('translator')->get('showInvoice.no_records'); ?></td>
                                            </tr>
                                        <?php else: ?>
                                            <?php $__currentLoopData = $combinedWorkData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $clientId => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><strong><?php echo e($clientId); ?></strong></td>
                                                    <td>
                                                        <strong><?php echo e($data['client_name']); ?></strong>
                                                        <?php if(!empty($data['customer_data']) && $data['customer_hours'] > 0): ?>
                                                            <ul style="list-style: none; padding-left: 0; margin: 0;">
                                                                <?php $__currentLoopData = $data['customer_data']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <?php if($customer['hours'] > 0): ?>
                                                                        <li><strong><?php echo e($customer['name']); ?></strong></li>
                                                                    <?php endif; ?>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </ul>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <ul>
                                                            <?php if($data['client_hours'] > 0): ?>
                                                                <?php $__currentLoopData = $data['client_wells']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $well): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <?php
                                                                        $totalHoursWorked = $well['hours'];
                                                                        $totalHours = floor($totalHoursWorked);
                                                                        $minutes = round(($totalHoursWorked - $totalHours) * 60);
                                                                        if ($minutes >= 60) {
                                                                            $totalHours += floor($minutes / 60);
                                                                            $minutes = $minutes % 60;
                                                                        }
                                                                        $locale = app()->getLocale();
                                                                        if ($locale === 'ae') {
                                                                            $formattedHours = convertToArabicNumbers($totalHours);
                                                                            $formattedMinutes = convertToArabicNumbers($minutes);
                                                                            $hoursLabel = trans('hours.hours');
                                                                            $minutesLabel = trans('hours.minutes');
                                                                        } else {
                                                                            $formattedHours = $totalHours;
                                                                            $formattedMinutes = $minutes;
                                                                            $hoursLabel = trans('hours.hours');
                                                                            $minutesLabel = trans('hours.minutes');
                                                                        }
                                                                    ?>
                                                                    <div>
                                                                    <strong><?php echo e($well['name']); ?>: <?php echo e($formattedHours); ?> <?php echo e($hoursLabel); ?>, <?php echo e($formattedMinutes); ?> <?php echo e($minutesLabel); ?></strong></div>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            <?php endif; ?>
                                                            <?php if($data['customer_hours'] > 0): ?>
                                                                <?php $__currentLoopData = $data['customer_data']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <?php if($customer['hours'] > 0): ?>
                                                                        <?php $__currentLoopData = $customer['wells']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $well): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <?php
                                                                                $totalHoursWorked = $well['hours'];
                                                                                $totalHours = floor($totalHoursWorked);
                                                                                $minutes = round(($totalHoursWorked - $totalHours) * 60);
                                                                                if ($minutes >= 60) {
                                                                                    $totalHours += floor($minutes / 60);
                                                                                    $minutes = $minutes % 60;
                                                                                }
                                                                                $locale = app()->getLocale();
                                                                                if ($locale === 'ae') {
                                                                                    $formattedHours = convertToArabicNumbers($totalHours);
                                                                                    $formattedMinutes = convertToArabicNumbers($minutes);
                                                                                    $hoursLabel = trans('hours.hours');
                                                                                    $minutesLabel = trans('hours.minutes');
                                                                                } else {
                                                                                    $formattedHours = $totalHours;
                                                                                    $formattedMinutes = $minutes;
                                                                                    $hoursLabel = trans('hours.hours');
                                                                                    $minutesLabel = trans('hours.minutes');
                                                                                }
                                                                            ?>
                                                                            <div>
                                                                                <strong><?php echo e($customer['name']); ?> - <?php echo e($well['name']); ?>: <?php echo e($formattedHours); ?> <?php echo e($hoursLabel); ?>, <?php echo e($formattedMinutes); ?> <?php echo e($minutesLabel); ?></strong>
                                                                            </div>
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    <?php endif; ?>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            <?php endif; ?>
                                                        </ul>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Total Remaining Amount Box -->
                    <div class="card mt-4">
                        <div class="card-body text-center">
                            <h5 class="card-title mb-3"><?php echo app('translator')->get('showInvoice.total_remaining_amount'); ?></h5>
                            <?php
                                $totalRemaining = 0;
                                if (!empty($separatedPayments)) {
                                    foreach ($separatedPayments as $payment) {
                                        $totalRemaining += $payment['remaining_amount'];
                                    }
                                }
                            ?>
                            <p class="fs-4"><strong><?php
                if (app()->getLocale() == 'ae') {
                    echo convertToArabicNumbers(number_format($totalRemaining, 2));
                } else {
                    echo number_format($totalRemaining, 2);
                }
            ?> <?php echo e(__('showInvoice.currency')); ?></strong></p>
                        </div>
                    </div>

                    <div class="hstack gap-2 justify-content-end d-print-none mt-4">
                        <a href="javascript:window.print()" class="btn btn-success"><i class="ri-printer-line align-bottom me-1"></i> <?php echo app('translator')->get('client-invoice.print'); ?></a>
                    </div>
                    <!-- Back Button -->
                    <div class="d-flex justify-content-end mt-4">
                        <a href="<?php echo e(route('invoices.index')); ?>" class="btn btn-secondary">
                            <i class="mdi mdi-arrow-left me-1"></i> <?php echo app('translator')->get('showInvoice.back_to_invoices'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script src="<?php echo e(URL::asset('/assets/libs/apexcharts/apexcharts.min.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('/assets/js/pages/dashboard-client-statistics-projects.init.js')); ?>"></script>
    <script src="<?php echo e(URL::asset('/assets/js/app.min.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\Beer-Elshabab\resources\views/invoices/showInvoice.blade.php ENDPATH**/ ?>