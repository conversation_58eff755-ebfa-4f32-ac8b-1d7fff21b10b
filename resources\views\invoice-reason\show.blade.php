@extends('layouts.master')
@section('css')
    <style>
        @media print {
            /* إجبار الجدول على عدم الانقسام بين الصفحات */
            .table-responsive {
                display: block !important;
                width: 100% !important;
                overflow-x: auto !important;
                -webkit-print-color-adjust: exact !important;
            }

            .table {
                width: 100% !important;
                page-break-inside: auto !important;
                page-break-before: auto !important;
                page-break-after: auto !important;
            }

            /* منع تقسيم الصفوف بين الصفحات */
            tr {
                page-break-inside: avoid !important;
                page-break-before: auto !important;
                page-break-after: auto !important;
            }

            /* جعل الصفحة مطولة ومنع التكرار على صفحات متعددة */
            body {
                height: auto !important;
                overflow: visible !important;
            }

            /* إخفاء العناصر غير الضرورية أثناء الطباعة */
            .d-print-none {
                display: none !important;
            }

            /* تقليل الهوامش */
            /*@page {*/
                margin: 5mm !important; /* تقليل الهوامش لاستيعاب محتوى أكبر
            }

            /* جعل الكارد يظهر بشكل كامل دون تقسيم */
            .card {
                page-break-inside: avoid !important;
            }

            /* تقليل حجم الخطوط لاستيعاب 56 عميل */
            .table th, .table td {
                font-size: 10px !important; /* تقليل حجم الخط */
                padding: 4px !important; /* تقليل المسافات الداخلية */
            }

            /* جعل الجدول يظهر بشكل أفقي أفضل */
            .table-responsive {
                white-space: nowrap !important; /* منع التفاف النصوص */
            }
        }
    </style>
@endsection
@section('content')
    @component('components.breadcrumb')
        @slot('li_1')
            {{ __('showInvoice.management') }}
        @endslot
        @slot('title')
            {{ __('showInvoice.invoice_details') }}
        @endslot
    @endcomponent

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <!-- Success and Error Messages -->
                    @if(session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @elseif(session('error'))
                        <div class="alert alert-danger">{{ session('error') }}</div>
                    @endif

                    <!-- Invoice Summary -->
                    <div class="invoice-summary mb-4 text-center">
                        <h4 class="card-title mb-3">{{ __('showInvoice.invoice_summary') }}</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="fs-5">
                                    <strong>{{ __('showInvoice.total_amount_due') }}</strong>
                                    {{ number_format($invoice->total_amount_due, 2) }} {{ __('showInvoice.currency') }}
                                </p>
                            </div>
                            <div class="col-md-6">
                                @php
                                    // Parse the start and end dates from the invoice
                                    $formattedStartDate = \Carbon\Carbon::parse($invoice->start_date);
                                    $formattedEndDate = \Carbon\Carbon::parse($invoice->end_date);

                                    // Check the current locale
                                    if (app()->getLocale() == 'ae') {
                                        // Arabic locale: Format the dates with Arabic numerals and Arabic text
                                        $formattedStartDate = $formattedStartDate->locale('ar')->translatedFormat('d F Y');
                                        $formattedEndDate = $formattedEndDate->locale('ar')->translatedFormat('d F Y');
                                        $separator = 'إلى'; // Arabic 'to'

                                        // Convert numbers to Arabic script
                                        $formattedStartDate = preg_replace_callback('/\d/', function ($matches) {
                                            return convertToArabicNumbers($matches[0]);
                                        }, $formattedStartDate);

                                        $formattedEndDate = preg_replace_callback('/\d/', function ($matches) {
                                            return convertToArabicNumbers($matches[0]);
                                        }, $formattedEndDate);

                                    } else {
                                        // English locale: Format the dates in standard English
                                        $formattedStartDate = $formattedStartDate->format('F d, Y');
                                        $formattedEndDate = $formattedEndDate->format('F d, Y');
                                        $separator = 'to'; // English 'to'
                                    }
                                @endphp

                                <p class="fs-5">
                                    <strong>{{ __('showInvoice.period') }}</strong>
                                    {{ $formattedStartDate }} {{ $separator }} {{ $formattedEndDate }}
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p class="fs-5"><strong>{{ __('showInvoice.reason') }}</strong> {{ $invoice->reason }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Wells Information -->
                    @if(!empty($wellsInfo))
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">معلومات الآبار وأسعار الساعات</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover align-middle table-bordered mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>اسم البئر</th>
                                            <th>إجمالي الساعات</th>
                                            <th>النسبة المئوية</th>
                                            <th>المبلغ المخصص</th>
                                            <th>سعر الساعة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($wellsInfo as $wellInfo)
                                        <tr>
                                            <td><strong>{{ $wellInfo['name'] }}</strong></td>
                                            <td>{{ number_format($wellInfo['total_hours'], 2) }} ساعة</td>
                                            <td>{{ number_format($wellInfo['percentage'], 2) }}%</td>
                                            <td>{{ number_format($wellInfo['allocated_amount'], 2) }} جنيه</td>
                                            <td><strong>{{ number_format($wellInfo['hourly_rate'], 2) }} جنيه/ساعة</strong></td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Client Breakdown Table -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{{ __('showInvoice.client_breakdown') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover align-middle table-bordered mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="8%">المعرف</th>
                                            <th width="20%">اسم العميل/المشارك</th>
                                            <th width="10%">النوع</th>
                                            <th width="12%">الساعات</th>
                                            <th width="15%">تفاصيل الآبار</th>
                                            <th width="15%">المبلغ المستحق</th>
                                            <th width="15%">إجمالي العميل</th>
                                            <th width="10%">حالة الدفع</th>
                                            <th width="10%">المتبقي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($clientData as $data)
                                            {{-- صف العميل الأساسي --}}
                                            <tr>
                                                <td class="text-center fw-bold">{{ $data['client_id'] }}</td>
                                                <td>
                                                    <strong>{{ $data['client'] }}</strong>
                                                    <br><small class="text-muted">العميل الأساسي</small>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge bg-secondary">أساسي</span>
                                                </td>
                                                <td class="text-center">
                                                    @php
                                                        $clientHours = $data['client_hours'];
                                                        $totalHours = floor($clientHours);
                                                        $minutes = round(($clientHours - $totalHours) * 60);
                                                        if ($minutes >= 60) {
                                                            $totalHours += floor($minutes / 60);
                                                            $minutes = $minutes % 60;
                                                        }
                                                        $formattedHours = convertToArabicNumbers($totalHours);
                                                        $formattedMinutes = convertToArabicNumbers($minutes);
                                                    @endphp
                                                    <span class="fw-bold">{{ $formattedHours }} ساعة و {{ $formattedMinutes }} دقيقة</span>
                                                </td>
                                                <td class="text-center">
                                                    @if(!empty($data['client_wells']))
                                                        @foreach($data['client_wells'] as $well)
                                                            <div class="mb-1">
                                                                <strong>{{ $well['name'] }}:</strong>
                                                                @php
                                                                    $wellHours = $well['hours'];
                                                                    $hours = floor($wellHours);
                                                                    $minutes = round(($wellHours - $hours) * 60);
                                                                    if ($minutes >= 60) {
                                                                        $hours += floor($minutes / 60);
                                                                        $minutes = $minutes % 60;
                                                                    }
                                                                    $formattedHours = convertToArabicNumbers($hours);
                                                                    $formattedMinutes = convertToArabicNumbers($minutes);
                                                                @endphp
                                                                {{ $formattedHours }} ساعة و {{ $formattedMinutes }} دقيقة
                                                                <br><small class="text-muted">({{ number_format($well['hourly_rate'], 2) }} جنيه/ساعة)</small>
                                                            </div>
                                                        @endforeach
                                                    @endif
                                                </td>
                                                <td class="text-center">
                                                    <span class="fw-bold">{{ number_format($data['client_amount'], 2) }}</span>
                                                    <br><small class="text-muted">جنيه</small>
                                                </td>
                                                <td class="text-center" rowspan="{{ count($data['customer_details']) + 1 }}">
                                                    <div class="border rounded p-2" style="background-color: #f8f9fa;">
                                                        <strong class="fs-5">{{ number_format($data['total_amount'], 2) }}</strong>
                                                        <br><small class="text-muted">جنيه</small>
                                                        <hr class="my-1">
                                                        @php
                                                            $totalHours = $data['total_hours'];
                                                            $hours = floor($totalHours);
                                                            $minutes = round(($totalHours - $hours) * 60);
                                                            if ($minutes >= 60) {
                                                                $hours += floor($minutes / 60);
                                                                $minutes = $minutes % 60;
                                                            }
                                                            $formattedHours = convertToArabicNumbers($hours);
                                                            $formattedMinutes = convertToArabicNumbers($minutes);
                                                        @endphp
                                                        <small class="text-muted">{{ $formattedHours }} ساعة و {{ $formattedMinutes }} دقيقة</small>
                                                    </div>
                                                </td>
                                                <td class="text-center" rowspan="{{ count($data['customer_details']) + 1 }}">
                                                    @if($data['payment_status'] == 'paid')
                                                        <span class="badge bg-success fs-6">مدفوع</span>
                                                    @elseif($data['payment_status'] == 'partial')
                                                        <span class="badge bg-warning fs-6">جزئي</span>
                                                    @else
                                                        <span class="badge bg-danger fs-6">غير مدفوع</span>
                                                    @endif
                                                </td>
                                                <td class="text-center" rowspan="{{ count($data['customer_details']) + 1 }}">
                                                    <span class="fw-bold">{{ number_format($data['remaining_amount'], 2) }}</span>
                                                    <br><small class="text-muted">جنيه</small>
                                                </td>
                                            </tr>

                                            {{-- صفوف العملاء المشاركين --}}
                                            @foreach($data['customer_details'] as $customer)
                                                <tr style="background-color: #f8f9fa;">
                                                    <td class="text-center">-</td>
                                                    <td>
                                                        <span class="fw-bold">{{ $customer['name'] }}</span>
                                                        <br><small class="text-muted">عميل مشارك</small>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge bg-light text-dark">مشارك</span>
                                                    </td>
                                                    <td class="text-center">
                                                        @php
                                                            $customerHours = $customer['hours'];
                                                            $totalHours = floor($customerHours);
                                                            $minutes = round(($customerHours - $totalHours) * 60);
                                                            if ($minutes >= 60) {
                                                                $totalHours += floor($minutes / 60);
                                                                $minutes = $minutes % 60;
                                                            }
                                                            $formattedHours = convertToArabicNumbers($totalHours);
                                                            $formattedMinutes = convertToArabicNumbers($minutes);
                                                        @endphp
                                                        <span class="fw-bold">{{ $formattedHours }} ساعة و {{ $formattedMinutes }} دقيقة</span>
                                                    </td>
                                                    <td class="text-center">
                                                        @if(!empty($customer['wells']))
                                                            @foreach($customer['wells'] as $well)
                                                                <div class="mb-1">
                                                                    <strong>{{ $well['name'] }}:</strong>
                                                                    @php
                                                                        $wellHours = $well['hours'];
                                                                        $hours = floor($wellHours);
                                                                        $minutes = round(($wellHours - $hours) * 60);
                                                                        if ($minutes >= 60) {
                                                                            $hours += floor($minutes / 60);
                                                                            $minutes = $minutes % 60;
                                                                        }
                                                                        $formattedHours = convertToArabicNumbers($hours);
                                                                        $formattedMinutes = convertToArabicNumbers($minutes);
                                                                    @endphp
                                                                    {{ $formattedHours }} ساعة و {{ $formattedMinutes }} دقيقة
                                                                    <br><small class="text-muted">({{ number_format($well['hourly_rate'], 2) }} جنيه/ساعة)</small>
                                                                </div>
                                                            @endforeach
                                                        @endif
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="fw-bold">{{ number_format($customer['amount'], 2) }}</span>
                                                        <br><small class="text-muted">جنيه</small>
                                                    </td>
                                                    {{-- الأعمدة المدمجة تظهر فقط في الصف الأول --}}
                                                </tr>
                                            @endforeach
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار إدارة الأرصدة -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">إدارة الأرصدة السالبة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <p class="mb-2"><strong>حالة الأرصدة:</strong>
                                                @if($balanceStatus)
                                                    <span class="badge bg-success">تم إضافة المبالغ للأرصدة السالبة</span>
                                                @else
                                                    <span class="badge bg-warning">لم يتم إضافة المبالغ للأرصدة بعد</span>
                                                @endif
                                            </p>
                                            <p class="text-muted small">
                                                عند إضافة المبالغ للأرصدة، سيتم إضافة مبلغ سالب لكل عميل وعميل مشارك
                                                ليتم خصمه تلقائياً من الفواتير العادية القادمة.
                                            </p>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            @if(!$balanceStatus)
                                                <form action="{{ route('invoice-reason.add-to-balances', $invoice->id) }}" method="POST" style="display: inline;">
                                                    @csrf
                                                    <button type="submit" class="btn btn-success"
                                                            onclick="return confirm('هل أنت متأكد من إضافة هذه المبالغ للأرصدة السالبة؟')">
                                                        <i class="mdi mdi-plus-circle"></i> إضافة للأرصدة
                                                    </button>
                                                </form>
                                            @else
                                                <form action="{{ route('invoice-reason.remove-from-balances', $invoice->id) }}" method="POST" style="display: inline;">
                                                    @csrf
                                                    <button type="submit" class="btn btn-danger"
                                                            onclick="return confirm('هل أنت متأكد من إلغاء الأرصدة السالبة؟')">
                                                        <i class="mdi mdi-minus-circle"></i> إلغاء من الأرصدة
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Summary Section -->
                    <div class="row justify-content-center text-center mt-5">
                        <!-- Total Amount -->
                        <div class="col-md-3">
                            <div class="card p-4 border shadow-sm" style="background-color: #e3f2fd;">
                                <h6 class="text-uppercase"><strong>إجمالي المبلغ</strong></h6>
                                <p class="fs-4 mb-0 text-primary fw-bold">{{ number_format($invoice->total_amount_due, 2) }} جنيه</p>
                            </div>
                        </div>
                        <!-- Total Hours Worked -->
                        <div class="col-md-3">
                            <div class="card p-4 border shadow-sm" style="background-color: #f3e5f5;">
                                <h6 class="text-uppercase"><strong>إجمالي الساعات</strong></h6>
                                @php
                                    $totalHours = $totalHoursWorked;
                                    $hours = floor($totalHours);
                                    $minutes = round(($totalHours - $hours) * 60);
                                    if ($minutes >= 60) {
                                        $hours += floor($minutes / 60);
                                        $minutes = $minutes % 60;
                                    }
                                    $formattedHours = convertToArabicNumbers($hours);
                                    $formattedMinutes = convertToArabicNumbers($minutes);
                                @endphp
                                <p class="fs-4 mb-0 text-purple fw-bold">{{ $formattedHours }} ساعة و {{ $formattedMinutes }} دقيقة</p>
                            </div>
                        </div>
                        <!-- Hourly Rate -->
                        <div class="col-md-3">
                            <div class="card p-4 border shadow-sm" style="background-color: #e8f5e8;">
                                <h6 class="text-uppercase"><strong>سعر الساعة</strong></h6>
                                <p class="fs-4 mb-0 text-success fw-bold">{{ number_format($totalHoursWorked > 0 ? $invoice->total_amount_due / $totalHoursWorked : 0, 2) }} جنيه</p>
                            </div>
                        </div>
                        <!-- Number of Clients -->
                        <div class="col-md-3">
                            <div class="card p-4 border shadow-sm" style="background-color: #fff3e0;">
                                <h6 class="text-uppercase"><strong>عدد العملاء</strong></h6>
                                <p class="fs-4 mb-0 text-warning fw-bold">{{ count($clientData) }} عميل</p>
                            </div>
                        </div>
                    </div>
                    <div class="hstack gap-2 justify-content-end d-print-none mt-4">
                        <a href="javascript:window.print()" class="btn btn-success"><i class="ri-printer-line align-bottom me-1"></i> @lang('client-invoice.print')</a>
                    </div>
                    <!-- Back Button -->
                    <div class="d-flex justify-content-end mt-4">
                        <a href="{{ route('invoice-reason.index') }}" class="btn btn-secondary">
                            <i class="mdi mdi-arrow-left me-1"></i> {{ __('showInvoice.back_to_invoices') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script src="{{ URL::asset('/assets/libs/apexcharts/apexcharts.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/js/pages/dashboard-client-statistics-projects.init.js') }}"></script>
    <script src="{{ URL::asset('/assets/js/app.min.js') }}"></script>
@endsection
