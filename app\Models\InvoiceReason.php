<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InvoiceReason extends Model
{
    use HasFactory;

    protected $table = 'invoice_reasons';

    protected $fillable = ['total_amount_due', 'reason', 'start_date', 'end_date', 'wells_config', 'wells_hourly_rates'];
    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'wells_config' => 'array',
        'wells_hourly_rates' => 'array',
    ];
    public function clientPaymentReasons()
    {
        return $this->hasMany(ClientPaymentReason::class, 'invoice_id');
    }

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function clientPayments()
    {
        return $this->hasMany(ClientPaymentReason::class, 'invoice_id');
    }
}
