<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\UsersController;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/


Route::get('/', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

Auth::routes();
//Language Translation
Route::post('/update-user-status', [App\Http\Controllers\Auth\LoginController::class, 'updateStatus'])->name('update.userStatus');

Route::post('/logout', [App\Http\Controllers\Auth\LoginController::class, 'logout'])->name('logout');

Route::get('index/{locale}', [App\Http\Controllers\HomeController::class, 'lang']);
Route::middleware(['auth'])->group(function () {
    Route::get('/hours', function () { return view('hours'); })->name('hours');    // Other routes that require authentication
});


//Update User Details
Route::post('/update-profile/{id}', [App\Http\Controllers\HomeController::class, 'updateProfile'])->name('updateProfile');
Route::post('/update-password/{id}', [App\Http\Controllers\HomeController::class, 'updatePassword'])->name('updatePassword');

Route::get('/customers/{clientId}', [App\Http\Controllers\CustomerController::class, 'getCustomersByClient']);

    // Route to list all admin
    Route::middleware('admin')->group(function () {
        Route::get('/users', [UsersController::class, 'index'])->name('users.index');
        Route::get('/users/{id}', [UsersController::class, 'show'])->name('users.show');
        Route::get('/users/{id}/edit', [UsersController::class, 'edit'])->name('users.edit');
        Route::put('/users/{id}', [UsersController::class, 'update'])->name('users.update');
        Route::delete('/users/{id}', [UsersController::class, 'destroy'])->name('users.destroy');
        Route::get('/admin/dashboard', [App\Http\Controllers\HomeController::class, 'adminDashboards'])->name('admin.dashboard');
        Route::get('/api/get-login-activities', [App\Http\Controllers\Auth\LoginController::class, 'getLoginActivities']);
        Route::get('/clients/{id}', [App\Http\Controllers\ClientsController::class, 'showClient'])->name('clients.show');
        // Route::post('/user/status/update', [App\Http\Controllers\Auth\LoginController::class, 'updateStatus']);
        Route::get('/fetch-status', [App\Http\Controllers\Auth\LoginController::class, 'fetchStatus']);

        Route::resource('clients', App\Http\Controllers\ClientsController::class);
        Route::resource('customers', App\Http\Controllers\CustomerController::class);
        Route::resource('owners', App\Http\Controllers\OwnersController::class);
        Route::resource('wells', App\Http\Controllers\WellsController::class);
        Route::resource('invoices', App\Http\Controllers\InvoicesController::class);
        Route::get('invoices/{invoice}', [App\Http\Controllers\InvoicesController::class, 'show'])->name('invoices.show');
        Route::get('/invoices/view/{id}', [App\Http\Controllers\InvoicesController::class, 'showInvoice'])->name('invoices.showInvoice');
        Route::get('/clients-well-summary', [App\Http\Controllers\ClientsController::class, 'wellSummary'])->name('clients.wellSummary');
        Route::get('/clients/{client}', [App\Http\Controllers\ClientsController::class, 'show'])->name('client.invoice');
        Route::post('/invoices/update-status', [App\Http\Controllers\ClientsController::class, 'updatePaymentStatus'])->name('invoices.updateStatus');
        Route::get('/payment-status', [App\Http\Controllers\ClientsController::class, 'showPaymentStatusForm'])->name('payment-status.form');
        Route::post('/payment-status/update', [App\Http\Controllers\ClientsController::class, 'updatePaymentStatus'])->name('payment-status.update');
        Route::get('/clients/get-unpaid', [App\Http\Controllers\ClientsController::class, 'getUnpaidClients'])->name('clients.getUnpaid');
        Route::get('/get-amount-due', [App\Http\Controllers\ClientsController::class, 'getAmountDue'])->name('get.amount.due');
        Route::get('clients/{clientId}/partial-payments', [App\Http\Controllers\ClientsController::class, 'showPartialPayments'])->name('partial-payments.index');

        // Route::resource('invoice-reason', App\Http\Controllers\InvoiceReasonController::class);
        Route::get('invoice-reason', [App\Http\Controllers\InvoiceReasonController::class, 'index'])->name('invoice-reason.index');
        Route::get('invoice-reason/create', [App\Http\Controllers\InvoiceReasonController::class, 'create'])->name('invoice-reason.create');
        Route::post('invoice-reason/store', [App\Http\Controllers\InvoiceReasonController::class, 'store'])->name('invoice-reason.store');
        Route::post('invoice-reason/get-wells-data', [App\Http\Controllers\InvoiceReasonController::class, 'getWellsData'])->name('invoice-reason.get-wells-data');
        Route::post('invoice-reason/update-payment-status', [App\Http\Controllers\InvoiceReasonController::class, 'updatePaymentStatusReason'])->name('invoice-reason.update-payment-status');
        Route::post('invoice-reason/{id}/add-to-balances', [App\Http\Controllers\InvoiceReasonController::class, 'addToBalances'])->name('invoice-reason.add-to-balances');
        Route::post('invoice-reason/{id}/remove-from-balances', [App\Http\Controllers\InvoiceReasonController::class, 'removeFromBalances'])->name('invoice-reason.remove-from-balances');
        Route::get('payment-status-reason', [App\Http\Controllers\InvoiceReasonController::class, 'showUpdatePaymentForm'])->name('invoice-reason.update-payment-status-form');
        Route::post('invoice-reason/get-amount-due', [App\Http\Controllers\InvoiceReasonController::class, 'getAmountDue'])->name('invoice-reason.get-amount-due');
        Route::get('/invoice-reason/{id}', [App\Http\Controllers\InvoiceReasonController::class, 'show'])->name('invoice-reason.show');
        Route::get('invoice-reason/{id}/edit', [App\Http\Controllers\InvoiceReasonController::class, 'edit'])->name('invoice-reason.edit');
        Route::put('invoice-reason/{id}', [App\Http\Controllers\InvoiceReasonController::class, 'update'])->name('invoice-reason.update');
        Route::delete('invoice-reason/{id}', [App\Http\Controllers\InvoiceReasonController::class, 'destroy'])->name('invoice-reason.destroy');
        Route::get('/clients/{clientId}/partial-payments-reason', [App\Http\Controllers\InvoiceReasonController::class, 'showPartialPaymentReason'])->name('clients.partial-payments-reason');
            // View all invoices
        Route::get('invoice-normal', [App\Http\Controllers\InvoiceNormalController::class, 'index'])->name('invoice-normal.index');
        Route::get('invoice-normal/create', [App\Http\Controllers\InvoiceNormalController::class, 'create'])->name('invoice-normal.create');
        Route::post('invoice-normal/store', [App\Http\Controllers\InvoiceNormalController::class, 'store'])->name('invoice-normal.store');
        Route::get('invoice-normal/{id}', [App\Http\Controllers\InvoiceNormalController::class, 'show'])->name('invoice-normal.show');
        Route::get('invoice-normal/{id}/edit', [App\Http\Controllers\InvoiceNormalController::class, 'edit'])->name('invoice-normal.edit');
        Route::put('invoice-normal/{id}', [App\Http\Controllers\InvoiceNormalController::class, 'update'])->name('invoice-normal.update');
        Route::delete('invoice-normal/{id}', [App\Http\Controllers\InvoiceNormalController::class, 'destroy'])->name('invoice-normal.destroy');
        // Route to update payment status for normal invoices
        Route::post('/payment-status/normal/update', [App\Http\Controllers\InvoiceNormalController::class, 'updatePaymentStatusNormal'])->name('payment-status.normal.update');
        Route::post('get-amount-due', [App\Http\Controllers\InvoiceNormalController::class, 'getAmountDueNormal'])->name('invoice-normal.get-amount-due');
        Route::get('/payment-form/normal/update', [App\Http\Controllers\InvoiceNormalController::class, 'showUpdatePaymentFormNormal'])->name('invoice-normal.update-payment-form');
        Route::get('/partial-payments/normal/{clientId}', [App\Http\Controllers\InvoiceNormalController::class, 'showPartialPaymentNormal'])->name('partial-payments.normal');



    });

Route::prefix('dashboards/client')->group(function () {
    Route::get('/summary', [App\Http\Controllers\Dashboards\Client\PagesController::class, 'clientSummary'])->name('client.dashboard');
    Route::get('/invoice', [App\Http\Controllers\Dashboards\Client\PagesController::class, 'show'])->name('client.invoiceShow');
    Route::get('reason-invoice', [App\Http\Controllers\Dashboards\Client\PagesController::class, 'showReasonInvoice'])->name('client.reasonInvoice');
    Route::get('partial-payment-reason', [App\Http\Controllers\Dashboards\Client\PagesController::class, 'showPartialPaymentReason'])->name('dashboard.partial-payment-reason');
    Route::get('partial-payment-normal', [App\Http\Controllers\Dashboards\Client\PagesController::class, 'showPartialPaymentNormal'])->name('dashboard.partial-payment-normal');
    Route::get('partial-payment', [App\Http\Controllers\Dashboards\Client\PagesController::class, 'showPartialPayments'])->name('dashboard.partial-payment');
    Route::get('invoice-normal', [App\Http\Controllers\Dashboards\Client\PagesController::class, 'InvoiceNormal'])->name('client.InvoiceNormal');
    // Route::get('invoice-normal/{invoiceNormalId?}', [App\Http\Controllers\Dashboards\Client\PagesController::class, 'InvoiceNormal'])->name('client.InvoiceNormal');
});

Route::get('{any}', [App\Http\Controllers\HomeController::class, 'index'])->name('index');

