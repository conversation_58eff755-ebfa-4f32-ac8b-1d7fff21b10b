<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientReasonBalance extends Model
{
    use HasFactory;

    protected $table = 'client_balances';

    protected $fillable = [
        'client_id',
        'customer_id',
        'invoice_reason_id',
        'amount',
        'status',
        'description'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
    ];

    // العلاقات
    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function invoiceReason()
    {
        return $this->belongsTo(InvoiceReason::class);
    }

    // دوال مساعدة
    public static function getActiveBalance($clientId, $customerId = null)
    {
        return self::where('client_id', $clientId)
            ->where('customer_id', $customerId)
            ->where('status', 'active')
            ->sum('amount');
    }

    public static function hasBalanceFromInvoice($invoiceReasonId)
    {
        return self::where('invoice_reason_id', $invoiceReasonId)
            ->where('status', 'active')
            ->exists();
    }

    public static function addToBalance($clientId, $customerId, $invoiceReasonId, $amount, $description = null)
    {
        return self::create([
            'client_id' => $clientId,
            'customer_id' => $customerId,
            'invoice_reason_id' => $invoiceReasonId,
            'amount' => -abs($amount), // دائماً سالب
            'status' => 'active',
            'description' => $description
        ]);
    }

    public static function removeFromBalance($invoiceReasonId)
    {
        return self::where('invoice_reason_id', $invoiceReasonId)
            ->update(['status' => 'cancelled']);
    }
}
