[2025-07-17 21:45:41] local.INFO: Getting wells data for period: 2025-01-01 00:00:00 to 2025-11-17 00:00:00  
[2025-07-17 21:45:41] local.INFO: Wells data found: 2 wells  
[2025-07-17 21:45:41] local.INFO: Wells data: [{"id":1,"name":"\u0627\u0644\u0628\u0626\u0631 \u0627\u0644\u0643\u0628\u064a\u0631","total_hours":1307.9166666666667},{"id":2,"name":"\u0627\u0644\u0628\u0626\u0631 \u0627\u0644\u0635\u063a\u064a\u0631","total_hours":202.25}]  
[2025-07-17 21:46:16] local.INFO: Client 1 - Well 1: 1 hours * 25.995539980886 = 25.995539980886  
[2025-07-17 21:46:16] local.INFO: Client 1 - Well 1: 7 hours * 25.995539980886 = 181.9687798662  
[2025-07-17 21:46:16] local.INFO: Client 1 - Well 1: 30.083333333333 hours * 25.995539980886 = 782.03249442498  
[2025-07-17 21:46:16] local.INFO: Client 1 - Well 1: 4 hours * 25.995539980886 = 103.98215992354  
[2025-07-17 21:46:16] local.INFO: Client 1 - Well 2: 3 hours * 326.*********** = 978.98640296663  
[2025-07-17 21:46:16] local.INFO: Client 1 - Well 2: 24 hours * 326.*********** = 7831.891223733  
[2025-07-17 21:46:16] local.INFO: Client 1 - Well 1: 3 hours * 25.995539980886 = 77.986619942657  
[2025-07-17 21:46:16] local.INFO: Client 1 - Well 1: 1 hours * 25.995539980886 = 25.995539980886  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 2: 30.083333333333 hours * 326.*********** = 9817.0580964153  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 1: 247.98333333333 hours * 25.995539980886 = 6446.46065626  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 1: 24 hours * 25.995539980886 = 623.89295954126  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 2: 6 hours * 326.*********** = 1957.9728059333  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 1: 6 hours * 25.995539980886 = 155.97323988531  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 1: 6 hours * 25.995539980886 = 155.97323988531  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 1: 6 hours * 25.995539980886 = 155.97323988531  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 2: 7 hours * 326.*********** = 2284.3016069221  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 1: 6 hours * 25.995539980886 = 155.97323988531  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 1: 6 hours * 25.995539980886 = 155.97323988531  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 1: 8 hours * 25.995539980886 = 207.96431984709  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 1: 4 hours * 25.995539980886 = 103.98215992354  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 2: 5 hours * 326.*********** = 1631.6440049444  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 1: 2 hours * 25.995539980886 = 51.991079961771  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 1: 2 hours * 25.995539980886 = 51.991079961771  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 2: 2 hours * 326.*********** = 652.65760197775  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 1: 2 hours * 25.995539980886 = 51.991079961771  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 1: 1 hours * 25.995539980886 = 25.995539980886  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 1: 1 hours * 25.995539980886 = 25.995539980886  
[2025-07-17 21:46:16] local.INFO: Customer eslam - Well 1: 1 hours * 25.995539980886 = 25.995539980886  
[2025-07-17 21:46:16] local.INFO: Customer hady - Well 2: 24 hours * 326.*********** = 7831.891223733  
[2025-07-17 21:46:16] local.INFO: Customer hady - Well 1: 21.383333333333 hours * 25.995539980886 = 555.87129659127  
[2025-07-17 21:46:16] local.INFO: Customer hady - Well 1: 6 hours * 25.995539980886 = 155.97323988531  
[2025-07-17 21:46:16] local.INFO: Customer hady - Well 2: 8 hours * 326.*********** = 2610.630407911  
[2025-07-17 21:46:16] local.INFO: Customer hady - Well 1: 7 hours * 25.995539980886 = 181.9687798662  
[2025-07-17 21:46:16] local.INFO: Client 2 - Well 1: 2 hours * 25.995539980886 = 51.991079961771  
[2025-07-17 21:46:16] local.INFO: Client 2 - Well 1: 3 hours * 25.995539980886 = 77.986619942657  
[2025-07-17 21:46:16] local.INFO: Client 2 - Well 1: 33 hours * 25.995539980886 = 857.85281936923  
[2025-07-17 21:46:16] local.INFO: Client 2 - Well 1: 5.35 hours * 25.995539980886 = 139.07613889774  
[2025-07-17 21:46:16] local.INFO: Client 2 - Well 1: 16.033333333333 hours * 25.995539980886 = 416.79515769353  
[2025-07-17 21:46:16] local.INFO: Client 2 - Well 1: 7 hours * 25.995539980886 = 181.9687798662  
[2025-07-17 21:46:16] local.INFO: Client 2 - Well 2: 6 hours * 326.*********** = 1957.9728059333  
[2025-07-17 21:46:16] local.INFO: Client 2 - Well 2: 6 hours * 326.*********** = 1957.9728059333  
[2025-07-17 21:46:16] local.INFO: Client 2 - Well 1: 5 hours * 25.995539980886 = 129.97769990443  
[2025-07-17 21:46:16] local.INFO: Client 2 - Well 2: 23.166666666667 hours * 326.*********** = 7559.9505562423  
[2025-07-17 21:46:16] local.INFO: Client 2 - Well 1: 5 hours * 25.995539980886 = 129.97769990443  
[2025-07-17 21:46:16] local.INFO: Client 2 - Well 1: 4 hours * 25.995539980886 = 103.98215992354  
[2025-07-17 21:46:16] local.INFO: Client 2 - Well 1: 24 hours * 25.995539980886 = 623.89295954126  
[2025-07-17 21:46:16] local.INFO: Client 3 - Well 1: 33 hours * 25.995539980886 = 857.85281936923  
[2025-07-17 21:46:16] local.INFO: Client 3 - Well 1: 0.083333333333333 hours * 25.995539980886 = 2.1662949984071  
[2025-07-17 21:46:16] local.INFO: Client 3 - Well 1: 264 hours * 25.995539980886 = 6862.8225549538  
[2025-07-17 21:46:16] local.INFO: Client 3 - Well 2: 6 hours * 326.*********** = 1957.9728059333  
[2025-07-17 21:46:16] local.INFO: Client 3 - Well 1: 8 hours * 25.995539980886 = 207.96431984709  
[2025-07-17 21:46:16] local.INFO: Client 3 - Well 2: 5 hours * 326.*********** = 1631.6440049444  
[2025-07-17 21:46:16] local.INFO: Client 3 - Well 1: 5 hours * 25.995539980886 = 129.97769990443  
[2025-07-17 21:46:16] local.INFO: Client 3 - Well 1: 4 hours * 25.995539980886 = 103.98215992354  
[2025-07-17 21:46:16] local.INFO: Client 3 - Well 2: 5 hours * 326.*********** = 1631.6440049444  
[2025-07-17 21:46:16] local.INFO: Client 4 - Well 2: 5 hours * 326.*********** = 1631.6440049444  
[2025-07-17 21:46:16] local.INFO: Client 4 - Well 1: 2 hours * 25.995539980886 = 51.991079961771  
[2025-07-17 21:46:16] local.INFO: Client 5 - Well 2: 2 hours * 326.*********** = 652.65760197775  
[2025-07-17 21:46:16] local.INFO: Client 5 - Well 1: 7 hours * 25.995539980886 = 181.9687798662  
[2025-07-17 21:46:16] local.INFO: Client 5 - Well 1: 16 hours * 25.995539980886 = 415.92863969417  
[2025-07-17 21:46:16] local.INFO: Client 5 - Well 2: 2 hours * 326.*********** = 652.65760197775  
[2025-07-17 21:46:16] local.INFO: Client 6 - Well 1: 456 hours * 25.995539980886 = 11853.966231284  
[2025-07-17 21:46:16] local.INFO: Client 6 - Well 1: 5 hours * 25.995539980886 = 129.97769990443  
[2025-07-17 21:46:16] local.INFO: Client 8 - Well 2: 29 hours * 326.*********** = 9463.**********  
[2025-07-17 21:46:16] local.INFO: Client 8 - Well 2: 4 hours * 326.*********** = 1305.**********  
[2025-07-17 21:46:17] local.ERROR: Unsupported operand types: float * string {"userId":1,"exception":"[object] (TypeError(code: 0): Unsupported operand types: float * string at D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Controllers\\InvoiceReasonController.php:798)
[stacktrace]
#0 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\InvoiceReasonController->show('9')
#1 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('show', Array)
#2 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\InvoiceReasonController), 'show')
#3 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#4 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#5 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\AdminMiddleware.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\LastSeenMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\LastSeenMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\Localization.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#20 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\Beer-Elshabab\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\UpdateUserStatus.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UpdateUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\Beer-Elshabab\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 {main}
"} 
