[2025-07-17 22:43:29] local.ERROR: Error adding to regular invoice: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 (Connection: mysql, SQL: insert into `client_balances` (`client_id`, `customer_id`, `invoice_reason_id`, `amount`, `status`, `description`, `updated_at`, `created_at`) values (1, ?, 0, -10008.*********, applied_to_invoice, مبلغ إضافي مطبق على الفاتورة العادية رقم 383, 2025-07-17 22:43:29, 2025-07-17 22:43:29))  
[2025-07-17 22:43:44] local.ERROR: Error adding to regular invoice: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 (Connection: mysql, SQL: insert into `client_balances` (`client_id`, `customer_id`, `invoice_reason_id`, `amount`, `status`, `description`, `updated_at`, `created_at`) values (1, ?, 0, -10008.*********, applied_to_invoice, مبلغ إضافي مطبق على الفاتورة العادية رقم 386, 2025-07-17 22:43:44, 2025-07-17 22:43:44))  
[2025-07-17 22:48:21] local.ERROR: Error adding to regular invoice: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`beer-el-shabab`.`client_balances`, CONSTRAINT `client_balances_invoice_reason_id_foreign` FOREIGN KEY (`invoice_reason_id`) REFERENCES `invoice_reasons` (`id`) ON DELETE CASCADE) (Connection: mysql, SQL: insert into `client_balances` (`client_id`, `customer_id`, `invoice_reason_id`, `amount`, `status`, `description`, `updated_at`, `created_at`) values (1, ?, 999999, -10008.*********, active, مبلغ إضافي من فاتورة أسباب مطبق على الفاتورة العادية رقم 386, 2025-07-17 22:48:20, 2025-07-17 22:48:20))  
[2025-07-17 22:48:55] local.ERROR: Error adding to regular invoice: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`beer-el-shabab`.`client_balances`, CONSTRAINT `client_balances_invoice_reason_id_foreign` FOREIGN KEY (`invoice_reason_id`) REFERENCES `invoice_reasons` (`id`) ON DELETE CASCADE) (Connection: mysql, SQL: insert into `client_balances` (`client_id`, `customer_id`, `invoice_reason_id`, `amount`, `status`, `description`, `updated_at`, `created_at`) values (1, ?, 999999, -10008.*********, active, مبلغ إضافي من فاتورة أسباب مطبق على الفاتورة العادية رقم 381, 2025-07-17 22:48:55, 2025-07-17 22:48:55))  
[2025-07-17 22:52:18] local.INFO: Starting addToRegularInvoice {"invoice_reason_id":"9","request_data":{"_token":"CrFTaOOTcBRQZlNUIgOIOW8gjPA4FUOce4qcQTC7","regular_invoice_id":"386"}} 
[2025-07-17 22:52:18] local.INFO: Found invoice reason {"invoice_reason":{"id":9,"client_id":null,"total_amount_due":"100000.00","reason":"تيست","start_date":"2024-12-31T22:00:00.000000Z","end_date":"2025-11-16T22:00:00.000000Z","wells_config":"[{\"id\":\"1\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0643\\u0628\\u064a\\u0631\",\"total_hours\":\"1307.9166666666667\",\"percentage\":\"34\",\"allocated_amount\":\"34000.00\"},{\"id\":\"2\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0635\\u063a\\u064a\\u0631\",\"total_hours\":\"202.25\",\"percentage\":\"66\",\"allocated_amount\":\"66000.00\"}]","wells_hourly_rates":"{\"1\":25.99553998088563,\"2\":326.3288009888752}","created_at":"2025-07-17T19:46:16.000000Z","updated_at":"2025-07-17T19:46:16.000000Z"}} 
[2025-07-17 22:52:18] local.INFO: Reason client data {"data":[{"client_id":1,"client_name":"کوثر یوسف احمد محمد","client_amount":10008.838760818775,"customers":[{"id":1,"name":"eslam","amount":24739.760271019204},{"id":2,"name":"hady","amount":11336.33494798679}]},{"client_id":2,"client_name":"جوهرة سليمان عبد الكريم","client_amount":14189.397283113554,"customers":[]},{"client_id":3,"client_name":"تامر فاروق محمد سنوسي","client_amount":13386.026664818497,"customers":[]},{"client_id":4,"client_name":"شريف عبد الله على سيد","client_amount":1683.635084906147,"customers":[]},{"client_id":5,"client_name":"مها حامد منصور محمد","client_amount":1903.2126235158703,"customers":[]},{"client_id":6,"client_name":"أ سعد منصور عبد السلام","client_amount":11983.943931188276,"customers":[]},{"client_id":8,"client_name":"نصره بشندي عطية عوض","client_amount":10768.85043263288,"customers":[]}]} 
[2025-07-17 22:52:18] local.INFO: Regular invoice clients {"clients":{"1":true,"2":true,"3":true,"4":true,"5":true,"6":true,"8":true}} 
[2025-07-17 22:52:18] local.INFO: Applying amounts to regular invoice {"reason_client_data_count":7,"regular_invoice_clients_count":7} 
[2025-07-17 22:52:18] local.INFO: Processing client {"client_id":1,"client_name":"کوثر یوسف احمد محمد","has_hours_in_regular":true,"client_amount":10008.838760818775,"customers_count":2} 
[2025-07-17 22:52:18] local.ERROR: Error adding to regular invoice {"message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`beer-el-shabab`.`client_balances`, CONSTRAINT `client_balances_invoice_reason_id_foreign` FOREIGN KEY (`invoice_reason_id`) REFERENCES `invoice_reasons` (`id`) ON DELETE CASCADE) (Connection: mysql, SQL: insert into `client_balances` (`client_id`, `customer_id`, `invoice_reason_id`, `amount`, `status`, `description`, `updated_at`, `created_at`) values (1, ?, 999999, -10008.*********, active, مبلغ إضافي من فاتورة أسباب مطبق على الفاتورة العادية رقم 386, 2025-07-17 22:52:18, 2025-07-17 22:52:18))","file":"D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":829,"trace":"#0 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `cl...', Array, Object(Closure))
#1 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('insert into `cl...', Array, Object(Closure))
#2 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(540): Illuminate\\Database\\Connection->statement('insert into `cl...', Array)
#3 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `cl...', Array)
#4 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3507): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `cl...', Array, 'id')
#5 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1333): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1298): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1137): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\ClientReasonBalance))
#11 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1026): tap(Object(App\\Models\\ClientReasonBalance), Object(Closure))
#12 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2334): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2346): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Controllers\\InvoiceReasonController.php(1389): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Controllers\\InvoiceReasonController.php(1342): App\\Http\\Controllers\\InvoiceReasonController->addAdditionalAmountToInvoice('386', 1, 10008.*********, Array)
#17 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Controllers\\InvoiceReasonController.php(1176): App\\Http\\Controllers\\InvoiceReasonController->applyAmountsToRegularInvoice(Array, Array, '386', '9')
#18 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\InvoiceReasonController->addToRegularInvoice('9', Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('addToRegularInv...', Array)
#20 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\InvoiceReasonController), 'addToRegularInv...')
#21 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#23 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\AdminMiddleware.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\LastSeenMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\LastSeenMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\Localization.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\Beer-Elshabab\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\UpdateUserStatus.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UpdateUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\Beer-Elshabab\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#72 {main}"} 
[2025-07-17 22:55:39] local.INFO: Starting addToRegularInvoice {"invoice_reason_id":"9","request_data":{"_token":"CrFTaOOTcBRQZlNUIgOIOW8gjPA4FUOce4qcQTC7","regular_invoice_id":"386"}} 
[2025-07-17 22:55:39] local.INFO: Found invoice reason {"invoice_reason":{"id":9,"client_id":null,"total_amount_due":"100000.00","reason":"تيست","start_date":"2024-12-31T22:00:00.000000Z","end_date":"2025-11-16T22:00:00.000000Z","wells_config":"[{\"id\":\"1\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0643\\u0628\\u064a\\u0631\",\"total_hours\":\"1307.9166666666667\",\"percentage\":\"34\",\"allocated_amount\":\"34000.00\"},{\"id\":\"2\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0635\\u063a\\u064a\\u0631\",\"total_hours\":\"202.25\",\"percentage\":\"66\",\"allocated_amount\":\"66000.00\"}]","wells_hourly_rates":"{\"1\":25.99553998088563,\"2\":326.3288009888752}","created_at":"2025-07-17T19:46:16.000000Z","updated_at":"2025-07-17T19:46:16.000000Z"}} 
[2025-07-17 22:55:39] local.INFO: Reason client data {"data":[{"client_id":1,"client_name":"کوثر یوسف احمد محمد","client_amount":10008.838760818775,"customers":[{"id":1,"name":"eslam","amount":24739.760271019204},{"id":2,"name":"hady","amount":11336.33494798679}]},{"client_id":2,"client_name":"جوهرة سليمان عبد الكريم","client_amount":14189.397283113554,"customers":[]},{"client_id":3,"client_name":"تامر فاروق محمد سنوسي","client_amount":13386.026664818497,"customers":[]},{"client_id":4,"client_name":"شريف عبد الله على سيد","client_amount":1683.635084906147,"customers":[]},{"client_id":5,"client_name":"مها حامد منصور محمد","client_amount":1903.2126235158703,"customers":[]},{"client_id":6,"client_name":"أ سعد منصور عبد السلام","client_amount":11983.943931188276,"customers":[]},{"client_id":8,"client_name":"نصره بشندي عطية عوض","client_amount":10768.85043263288,"customers":[]}]} 
[2025-07-17 22:55:39] local.INFO: Regular invoice clients {"clients":{"1":true,"2":true,"3":true,"4":true,"5":true,"6":true,"8":true}} 
[2025-07-17 22:55:39] local.INFO: Applying amounts to regular invoice {"reason_client_data_count":7,"regular_invoice_clients_count":7} 
[2025-07-17 22:55:39] local.INFO: Processing client {"client_id":1,"client_name":"کوثر یوسف احمد محمد","has_hours_in_regular":true,"client_amount":10008.838760818775,"customers_count":2} 
[2025-07-17 22:55:40] local.INFO: Processing client {"client_id":2,"client_name":"جوهرة سليمان عبد الكريم","has_hours_in_regular":true,"client_amount":14189.397283113554,"customers_count":0} 
[2025-07-17 22:55:40] local.INFO: Processing client {"client_id":3,"client_name":"تامر فاروق محمد سنوسي","has_hours_in_regular":true,"client_amount":13386.026664818497,"customers_count":0} 
[2025-07-17 22:55:40] local.INFO: Processing client {"client_id":4,"client_name":"شريف عبد الله على سيد","has_hours_in_regular":true,"client_amount":1683.635084906147,"customers_count":0} 
[2025-07-17 22:55:40] local.INFO: Processing client {"client_id":5,"client_name":"مها حامد منصور محمد","has_hours_in_regular":true,"client_amount":1903.2126235158703,"customers_count":0} 
[2025-07-17 22:55:40] local.INFO: Processing client {"client_id":6,"client_name":"أ سعد منصور عبد السلام","has_hours_in_regular":true,"client_amount":11983.943931188276,"customers_count":0} 
[2025-07-17 22:55:40] local.INFO: Processing client {"client_id":8,"client_name":"نصره بشندي عطية عوض","has_hours_in_regular":true,"client_amount":10768.85043263288,"customers_count":0} 
[2025-07-17 22:55:40] local.INFO: Finished applying amounts to regular invoice  
[2025-07-17 22:55:40] local.ERROR: Error adding to regular invoice {"message":"SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 (Connection: mysql, SQL: insert into `client_balances` (`client_id`, `customer_id`, `invoice_reason_id`, `amount`, `status`, `description`, `updated_at`, `created_at`) values (1, ?, 9, 0, processed, تم تطبيق فاتورة الأسباب على الفاتورة العادية رقم 386, 2025-07-17 22:55:40, 2025-07-17 22:55:40))","file":"D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":829,"trace":"#0 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `cl...', Array, Object(Closure))
#1 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('insert into `cl...', Array, Object(Closure))
#2 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(540): Illuminate\\Database\\Connection->statement('insert into `cl...', Array)
#3 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `cl...', Array)
#4 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3507): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `cl...', Array, 'id')
#5 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1333): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1298): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1137): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\ClientReasonBalance))
#11 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1026): tap(Object(App\\Models\\ClientReasonBalance), Object(Closure))
#12 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2334): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2346): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Controllers\\InvoiceReasonController.php(1185): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\InvoiceReasonController->addToRegularInvoice('9', Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('addToRegularInv...', Array)
#18 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\InvoiceReasonController), 'addToRegularInv...')
#19 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#20 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#21 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\AdminMiddleware.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\LastSeenMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\LastSeenMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\Localization.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\Beer-Elshabab\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\UpdateUserStatus.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UpdateUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\Beer-Elshabab\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 {main}"} 
[2025-07-17 22:58:17] local.INFO: Starting addToRegularInvoice {"invoice_reason_id":"11","request_data":{"_token":"CrFTaOOTcBRQZlNUIgOIOW8gjPA4FUOce4qcQTC7","regular_invoice_id":"386"}} 
[2025-07-17 22:58:17] local.INFO: Found invoice reason {"invoice_reason":{"id":11,"client_id":null,"total_amount_due":"100000.00","reason":"تيست","start_date":"2024-12-31T22:00:00.000000Z","end_date":"2025-11-16T22:00:00.000000Z","wells_config":"[{\"id\":\"1\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0643\\u0628\\u064a\\u0631\",\"total_hours\":\"1307.9166666666667\",\"percentage\":\"34\",\"allocated_amount\":\"34000.00\"},{\"id\":\"2\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0635\\u063a\\u064a\\u0631\",\"total_hours\":\"202.25\",\"percentage\":\"66\",\"allocated_amount\":\"66000.00\"}]","wells_hourly_rates":"{\"1\":25.99553998088563,\"2\":326.3288009888752}","created_at":"2025-07-17T19:52:13.000000Z","updated_at":"2025-07-17T19:52:13.000000Z"}} 
[2025-07-17 22:58:17] local.INFO: Reason client data {"data":[{"client_id":1,"client_name":"کوثر یوسف احمد محمد","client_amount":10008.838760818775,"customers":[{"id":1,"name":"eslam","amount":24739.760271019204},{"id":2,"name":"hady","amount":11336.33494798679}]},{"client_id":2,"client_name":"جوهرة سليمان عبد الكريم","client_amount":14189.397283113554,"customers":[]},{"client_id":3,"client_name":"تامر فاروق محمد سنوسي","client_amount":13386.026664818497,"customers":[]},{"client_id":4,"client_name":"شريف عبد الله على سيد","client_amount":1683.635084906147,"customers":[]},{"client_id":5,"client_name":"مها حامد منصور محمد","client_amount":1903.2126235158703,"customers":[]},{"client_id":6,"client_name":"أ سعد منصور عبد السلام","client_amount":11983.943931188276,"customers":[]},{"client_id":8,"client_name":"نصره بشندي عطية عوض","client_amount":10768.85043263288,"customers":[]}]} 
[2025-07-17 22:58:17] local.INFO: Regular invoice clients {"clients":{"1":true,"2":true,"3":true,"4":true,"5":true,"6":true,"8":true}} 
[2025-07-17 22:58:17] local.INFO: Applying amounts to regular invoice {"reason_client_data_count":7,"regular_invoice_clients_count":7} 
[2025-07-17 22:58:17] local.INFO: Processing client {"client_id":1,"client_name":"کوثر یوسف احمد محمد","has_hours_in_regular":true,"client_amount":10008.838760818775,"customers_count":2} 
[2025-07-17 22:58:17] local.INFO: Processing client {"client_id":2,"client_name":"جوهرة سليمان عبد الكريم","has_hours_in_regular":true,"client_amount":14189.397283113554,"customers_count":0} 
[2025-07-17 22:58:17] local.INFO: Processing client {"client_id":3,"client_name":"تامر فاروق محمد سنوسي","has_hours_in_regular":true,"client_amount":13386.026664818497,"customers_count":0} 
[2025-07-17 22:58:17] local.INFO: Processing client {"client_id":4,"client_name":"شريف عبد الله على سيد","has_hours_in_regular":true,"client_amount":1683.635084906147,"customers_count":0} 
[2025-07-17 22:58:17] local.INFO: Processing client {"client_id":5,"client_name":"مها حامد منصور محمد","has_hours_in_regular":true,"client_amount":1903.2126235158703,"customers_count":0} 
[2025-07-17 22:58:17] local.INFO: Processing client {"client_id":6,"client_name":"أ سعد منصور عبد السلام","has_hours_in_regular":true,"client_amount":11983.943931188276,"customers_count":0} 
[2025-07-17 22:58:17] local.INFO: Processing client {"client_id":8,"client_name":"نصره بشندي عطية عوض","has_hours_in_regular":true,"client_amount":10768.85043263288,"customers_count":0} 
[2025-07-17 22:58:17] local.INFO: Finished applying amounts to regular invoice  
[2025-07-17 22:58:17] local.INFO: Successfully added amounts to regular invoice  
[2025-07-17 23:09:07] local.INFO: Starting addToRegularInvoice {"invoice_reason_id":"9","request_data":{"_token":"CrFTaOOTcBRQZlNUIgOIOW8gjPA4FUOce4qcQTC7","regular_invoice_id":"386"}} 
[2025-07-17 23:09:07] local.INFO: Found invoice reason {"invoice_reason":{"id":9,"client_id":null,"total_amount_due":"100000.00","reason":"تيست","start_date":"2024-12-31T22:00:00.000000Z","end_date":"2025-11-16T22:00:00.000000Z","wells_config":"[{\"id\":\"1\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0643\\u0628\\u064a\\u0631\",\"total_hours\":\"1307.9166666666667\",\"percentage\":\"34\",\"allocated_amount\":\"34000.00\"},{\"id\":\"2\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0635\\u063a\\u064a\\u0631\",\"total_hours\":\"202.25\",\"percentage\":\"66\",\"allocated_amount\":\"66000.00\"}]","wells_hourly_rates":"{\"1\":25.99553998088563,\"2\":326.3288009888752}","created_at":"2025-07-17T19:46:16.000000Z","updated_at":"2025-07-17T19:46:16.000000Z"}} 
[2025-07-17 23:09:07] local.INFO: Reason client data {"data":[{"client_id":1,"client_name":"کوثر یوسف احمد محمد","client_amount":10008.838760818775,"customers":[{"id":1,"name":"eslam","amount":24739.760271019204},{"id":2,"name":"hady","amount":11336.33494798679}]},{"client_id":2,"client_name":"جوهرة سليمان عبد الكريم","client_amount":14189.397283113554,"customers":[]},{"client_id":3,"client_name":"تامر فاروق محمد سنوسي","client_amount":13386.026664818497,"customers":[]},{"client_id":4,"client_name":"شريف عبد الله على سيد","client_amount":1683.635084906147,"customers":[]},{"client_id":5,"client_name":"مها حامد منصور محمد","client_amount":1903.2126235158703,"customers":[]},{"client_id":6,"client_name":"أ سعد منصور عبد السلام","client_amount":11983.943931188276,"customers":[]},{"client_id":8,"client_name":"نصره بشندي عطية عوض","client_amount":10768.85043263288,"customers":[]}]} 
[2025-07-17 23:09:07] local.INFO: Regular invoice clients {"clients":{"1":true,"2":true,"3":true,"4":true,"5":true,"6":true,"8":true}} 
[2025-07-17 23:09:07] local.INFO: Applying amounts to regular invoice {"reason_client_data_count":7,"regular_invoice_clients_count":7} 
[2025-07-17 23:09:07] local.INFO: Processing client {"client_id":1,"client_name":"کوثر یوسف احمد محمد","has_hours_in_regular":true,"client_amount":10008.838760818775,"customers_count":2} 
[2025-07-17 23:09:07] local.INFO: Updated client payment {"client_id":1,"invoice_id":"386","additional_amount_added":46084.93397982477,"new_additional_paid":46084.93397982477,"new_remaining_amount":60235.64397982477} 
[2025-07-17 23:09:07] local.INFO: Processing client {"client_id":2,"client_name":"جوهرة سليمان عبد الكريم","has_hours_in_regular":true,"client_amount":14189.397283113554,"customers_count":0} 
[2025-07-17 23:09:07] local.INFO: Updated client payment {"client_id":2,"invoice_id":"386","additional_amount_added":14189.397283113554,"new_additional_paid":14189.397283113554,"new_remaining_amount":15995.407283113555} 
[2025-07-17 23:09:07] local.INFO: Processing client {"client_id":3,"client_name":"تامر فاروق محمد سنوسي","has_hours_in_regular":true,"client_amount":13386.026664818497,"customers_count":0} 
[2025-07-17 23:09:07] local.INFO: Updated client payment {"client_id":3,"invoice_id":"386","additional_amount_added":13386.026664818497,"new_additional_paid":13386.026664818497,"new_remaining_amount":24236.0266648185} 
[2025-07-17 23:09:07] local.INFO: Processing client {"client_id":4,"client_name":"شريف عبد الله على سيد","has_hours_in_regular":true,"client_amount":1683.635084906147,"customers_count":0} 
[2025-07-17 23:09:07] local.INFO: Updated client payment {"client_id":4,"invoice_id":"386","additional_amount_added":1683.635084906147,"new_additional_paid":1683.635084906147,"new_remaining_amount":1683.635084906147} 
[2025-07-17 23:09:07] local.INFO: Processing client {"client_id":5,"client_name":"مها حامد منصور محمد","has_hours_in_regular":true,"client_amount":1903.2126235158703,"customers_count":0} 
[2025-07-17 23:09:07] local.INFO: Updated client payment {"client_id":5,"invoice_id":"386","additional_amount_added":1903.2126235158703,"new_additional_paid":1903.2126235158703,"new_remaining_amount":2810.1726235158703} 
[2025-07-17 23:09:07] local.INFO: Processing client {"client_id":6,"client_name":"أ سعد منصور عبد السلام","has_hours_in_regular":true,"client_amount":11983.943931188276,"customers_count":0} 
[2025-07-17 23:09:07] local.INFO: Updated client payment {"client_id":6,"invoice_id":"386","additional_amount_added":11983.943931188276,"new_additional_paid":11983.943931188276,"new_remaining_amount":28832.663931188275} 
[2025-07-17 23:09:07] local.INFO: Processing client {"client_id":8,"client_name":"نصره بشندي عطية عوض","has_hours_in_regular":true,"client_amount":10768.85043263288,"customers_count":0} 
[2025-07-17 23:09:07] local.INFO: Updated client payment {"client_id":8,"invoice_id":"386","additional_amount_added":10768.85043263288,"new_additional_paid":10768.85043263288,"new_remaining_amount":13741.01043263288} 
[2025-07-17 23:09:07] local.INFO: Finished applying amounts to regular invoice  
[2025-07-17 23:09:07] local.INFO: Successfully added amounts to regular invoice  
[2025-07-17 23:34:39] local.ERROR: Undefined variable $data {"view":{"view":"D:\\laragon\\www\\Beer-Elshabab\\resources\\views\\invoices\\showInvoice.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-917930248 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1669</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-917930248\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","invoice":"<pre class=sf-dump id=sf-dump-1635306005 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Invoice</span> {<a class=sf-dump-ref>#1713</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">invoices</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>386</span>
    \"<span class=sf-dump-key>total_amount_due</span>\" => \"<span class=sf-dump-str title=\"8 characters\">50000.00</span>\"
    \"<span class=sf-dump-key>additional_due_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>second_additional_due_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>month</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-03-01</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-18 23:48:30</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-18 23:48:30</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>386</span>
    \"<span class=sf-dump-key>total_amount_due</span>\" => \"<span class=sf-dump-str title=\"8 characters\">50000.00</span>\"
    \"<span class=sf-dump-key>additional_due_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>second_additional_due_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>month</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-03-01</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-18 23:48:30</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-18 23:48:30</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>month</span>\" => \"<span class=sf-dump-str title=\"10 characters\">date:Y-m-d</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>clientPayments</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#1776</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:53</span> [ &#8230;53]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>wells</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#1759</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">total_amount_due</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">month</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"21 characters\">additional_due_amount</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"28 characters\">second_additional_due_amount</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-1635306005\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","combinedWorkData":"<pre class=sf-dump id=sf-dump-2079074366 data-indent-pad=\"  \"><span class=sf-dump-note>array:53</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1705;&#1608;&#1579;&#1585; &#1740;&#1608;&#1587;&#1601; &#1575;&#1581;&#1605;&#1583; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:4</span> [ &#8230;4]
      <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:4</span> [ &#8230;4]
    </samp>]
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>3.0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>323.98333333333</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>326.98333333333</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>256.56</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>14407.13</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>60898.62</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">partial</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"8 characters\">46084.93</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"6 characters\">150.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>customer_wells</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
  </samp>]
  <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"23 characters\">&#1580;&#1608;&#1607;&#1585;&#1577; &#1587;&#1604;&#1610;&#1605;&#1575;&#1606; &#1593;&#1576;&#1583; &#1575;&#1604;&#1603;&#1585;&#1610;&#1605;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>51.2</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>51.2</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>3594.56</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>17933.96</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">partial</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"8 characters\">14189.40</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"6 characters\">150.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">&#1578;&#1575;&#1605;&#1585; &#1601;&#1575;&#1585;&#1608;&#1602; &#1605;&#1581;&#1605;&#1583; &#1587;&#1606;&#1608;&#1587;&#1610;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>280.0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>280.0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>11036.1</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>24572.13</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">partial</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"8 characters\">13386.03</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"6 characters\">150.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>4</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">&#1588;&#1585;&#1610;&#1601; &#1593;&#1576;&#1583; &#1575;&#1604;&#1604;&#1607; &#1593;&#1604;&#1609; &#1587;&#1610;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>5.0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>5.0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>427.59</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>2261.23</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1683.64</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"6 characters\">150.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1605;&#1607;&#1575; &#1581;&#1575;&#1605;&#1583; &#1605;&#1606;&#1589;&#1608;&#1585; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>18.0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>18.0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>756.97</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>2810.18</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1903.21</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"6 characters\">150.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
      <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>6</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">&#1571; &#1587;&#1593;&#1583; &#1605;&#1606;&#1589;&#1608;&#1585; &#1593;&#1576;&#1583; &#1575;&#1604;&#1587;&#1604;&#1575;&#1605;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>456.0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>456.0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>16698.95</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>28832.89</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"8 characters\">11983.94</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"6 characters\">150.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>7</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">&#1593;&#1604;&#1609; &#1593;&#1575;&#1588;&#1585; &#1593;&#1604;&#1609; &#1587;&#1610;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>8</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1606;&#1589;&#1585;&#1607; &#1576;&#1588;&#1606;&#1583;&#1610; &#1593;&#1591;&#1610;&#1577; &#1593;&#1608;&#1590;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>33.0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>33.0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>2822.13</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>13740.98</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"8 characters\">10768.85</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"6 characters\">150.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>9</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"23 characters\">&#1606;&#1580;&#1575;&#1577; &#1605;&#1606;&#1589;&#1608;&#1585; &#1587;&#1604;&#1610;&#1605;&#1575;&#1606; &#1605;&#1589;&#1591;&#1601;&#1609;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>10</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">&#1603;&#1605;&#1575;&#1604; &#1605;&#1581;&#1605;&#1583; &#1601;&#1604;&#1575;&#1581;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>11</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1593;&#1576;&#1610;&#1585; &#1586;&#1603;&#1609; &#1605;&#1581;&#1605;&#1583; &#1593;&#1605;&#1575;&#1585;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>12</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1585;&#1588;&#1575; &#1593;&#1576;&#1583; &#1575;&#1604;&#1604;&#1607; &#1573;&#1576;&#1585;&#1575;&#1607;&#1610;&#1605; &#1605;&#1606;&#1589;&#1608;&#1585;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>13</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1588;&#1610;&#1605;&#1575;&#1569; &#1593;&#1604;&#1609; &#1605;&#1581;&#1605;&#1583; &#1593;&#1604;&#1609;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>14</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"25 characters\">&#1740;&#1575;&#1587;&#1585; &#1605;&#1581;&#1605;&#1583; &#1593;&#1576;&#1583; &#1575;&#1604;&#1583;&#1575;&#1610;&#1605; &#1582;&#1605;&#1610;&#1587;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>15</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"25 characters\">&#1593;&#1576;&#1583; &#1575;&#1604;&#1606;&#1575;&#1589;&#1585; &#1575;&#1581;&#1605;&#1583; &#1606;&#1608;&#1585; &#1575;&#1604;&#1583;&#1610;&#1606;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>16</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1607;&#1575;&#1606;&#1740; &#1593;&#1576;&#1583; &#1575;&#1604;&#1604;&#1607; &#1585;&#1601;&#1575;&#1593;&#1740; &#1587;&#1604;&#1740;&#1605;&#1575;&#1606;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>17</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1575;&#1605;&#1606;&#1607; &#1593;&#1604;&#1609; &#1575;&#1581;&#1605;&#1583; &#1581;&#1587;&#1610;&#1606;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>18</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">&#1606;&#1608;&#1585; &#1571;&#1576;&#1608; &#1576;&#1603;&#1585; &#1605;&#1583;&#1606;&#1609; &#1593;&#1604;&#1609;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>19</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"29 characters\">&#1571;&#1588;&#1585;&#1601; &#1605;&#1581;&#1605;&#1608;&#1583; &#1593;&#1576;&#1583; &#1575;&#1604;&#1604;&#1607; &#1593;&#1576;&#1583; &#1575;&#1604;&#1606;&#1576;&#1610;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>20</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1589;&#1575;&#1576;&#1585; &#1605;&#1581;&#1605;&#1583; &#1605;&#1581;&#1605;&#1608;&#1583; &#1606;&#1589;&#1585;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>21</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1605;&#1583;&#1610;&#1581;&#1577; &#1581;&#1587;&#1610;&#1606; &#1593;&#1576;&#1583; &#1575;&#1604;&#1587;&#1604;&#1575;&#1605; &#1581;&#1587;&#1610;&#1606;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>22</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1587;&#1607;&#1575;&#1605; &#1605;&#1576;&#1575;&#1585;&#1586; &#1606;&#1608;&#1585; &#1575;&#1604;&#1583;&#1610;&#1606; &#1576;&#1603;&#1585;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>23</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"23 characters\">&#1610;&#1608;&#1587;&#1601; &#1573;&#1576;&#1585;&#1575;&#1607;&#1610;&#1605; &#1605;&#1581;&#1605;&#1583; &#1605;&#1576;&#1575;&#1585;&#1586;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>24</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">&#1571;&#1605;&#1575;&#1604; &#1601;&#1578;&#1581;&#1740; &#1605;&#1581;&#1605;&#1583; &#1575;&#1576;&#1585;&#1575;&#1607;&#1740;&#1605;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>25</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1605;&#1585;&#1601;&#1578; &#1593;&#1576;&#1583; &#1575;&#1604;&#1608;&#1607;&#1575;&#1576; &#1576;&#1583;&#1585;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>26</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"25 characters\">&#1607;&#1583;&#1609; &#1593;&#1576;&#1583; &#1575;&#1604;&#1587;&#1604;&#1575;&#1605; &#1605;&#1581;&#1605;&#1583; &#1605;&#1581;&#1605;&#1608;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>27</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"29 characters\">&#1573;&#1576;&#1585;&#1575;&#1607;&#1610;&#1605; &#1593;&#1576;&#1583; &#1575;&#1604;&#1608;&#1607;&#1575;&#1576; &#1575;&#1576;&#1608; &#1575;&#1604;&#1587;&#1593;&#1608;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>28</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1606;&#1585;&#1605;&#1740;&#1606; &#1705;&#1605;&#1575;&#1604; &#1605;&#1581;&#1605;&#1583; &#1587;&#1740;&#1583; &#1593;&#1740;&#1587;&#1740;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>29</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"23 characters\">&#1605;&#1606;&#1575;&#1604; &#1576;&#1588;&#1606;&#1583;&#1610; &#1575;&#1581;&#1605;&#1583; &#1575;&#1587;&#1605;&#1575;&#1593;&#1740;&#1604;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>30</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"25 characters\">&#1606;&#1583;&#1610;&#1607; &#1593;&#1576;&#1583; &#1575;&#1604;&#1605;&#1608;&#1604;&#1609; &#1576;&#1588;&#1606;&#1583;&#1610; &#1587;&#1610;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>31</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#1605;&#1581;&#1605;&#1583; &#1593;&#1604;&#1740; &#1605;&#1581;&#1605;&#1583; &#1593;&#1604;&#1609;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>32</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1604;&#1740;&#1604;&#1740; &#1587;&#1740;&#1583; &#1575;&#1581;&#1605;&#1583; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>33</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"31 characters\">&#1605;&#1581;&#1605;&#1583; &#1593;&#1576;&#1583; &#1575;&#1604;&#1583;&#1575;&#1610;&#1605; &#1605;&#1581;&#1605;&#1583; &#1593;&#1576;&#1583; &#1575;&#1604;&#1583;&#1575;&#1610;&#1605;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>34</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1606;&#1608;&#1585;&#1607; &#1605;&#1581;&#1605;&#1583; &#1582;&#1604;&#1740;&#1604; &#1575;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>35</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"25 characters\">&#1582;&#1604;&#1610;&#1604; &#1594;&#1575;&#1574;&#1605; &#1593;&#1576;&#1583; &#1575;&#1604;&#1581;&#1605;&#1610;&#1583; &#1575;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>36</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#1607;&#1583;&#1609; &#1581;&#1587;&#1606; &#1605;&#1581;&#1605;&#1583; &#1581;&#1587;&#1610;&#1606;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>37</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1578;&#1575;&#1605;&#1585; &#1587;&#1593;&#1610;&#1583; &#1610;&#1608;&#1606;&#1587; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>38</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1607;&#1606;&#1575;&#1569; &#1587;&#1610;&#1583; &#1605;&#1581;&#1605;&#1583; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>39</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1605;&#1581;&#1605;&#1583; &#1583;&#1585;&#1583;&#1740;&#1585; &#1605;&#1581;&#1605;&#1583; &#1593;&#1576;&#1583; &#1575;&#1604;&#1587;&#1604;&#1575;&#1605;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>40</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">&#1605;&#1587;&#1578;&#1608;&#1585;&#1577; &#1605;&#1607;&#1583;&#1610; &#1587;&#1610;&#1583; &#1575;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>41</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1589;&#1576;&#1581;&#1740; &#1605;&#1606;&#1589;&#1608;&#1585; &#1593;&#1576;&#1583; &#1575;&#1604;&#1587;&#1604;&#1575;&#1605; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>42</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1571;&#1581;&#1605;&#1583; &#1610;&#1608;&#1606;&#1587; &#1605;&#1581;&#1605;&#1583; &#1610;&#1608;&#1606;&#1587;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>43</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1589;&#1576;&#1575;&#1581; &#1581;&#1575;&#1605;&#1583; &#1605;&#1581;&#1605;&#1583; &#1582;&#1604;&#1610;&#1604;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>44</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1601;&#1575;&#1591;&#1605;&#1607; &#1587;&#1593;&#1583; &#1605;&#1581;&#1605;&#1583; &#1587;&#1593;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>45</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1571;&#1581;&#1605;&#1583; &#1581;&#1575;&#1605;&#1583; &#1605;&#1581;&#1605;&#1583; &#1582;&#1604;&#1610;&#1604;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>46</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1605;&#1605;&#1583;&#1608;&#1581; &#1605;&#1581;&#1605;&#1583; &#1593;&#1608;&#1590; &#1575;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>47</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">&#1589;&#1575;&#1576;&#1585; &#1581;&#1587;&#1740;&#1606; &#1590;&#1581;&#1575;&#1608;&#1740; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>48</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1581;&#1604;&#1610;&#1605;&#1607; &#1593;&#1576;&#1583; &#1575;&#1604;&#1585;&#1581;&#1605;&#1606; &#1575;&#1581;&#1605;&#1583; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>49</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">&#1589;&#1575;&#1576;&#1585; &#1576;&#1588;&#1606;&#1583;&#1740; &#1581;&#1575;&#1601;&#1592;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>50</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#1606;&#1580;&#1575;&#1577; &#1606;&#1589;&#1585; &#1593;&#1605;&#1585; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>51</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">&#1607;&#1575;&#1583;&#1610;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>52</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">&#1601;&#1575;&#1583;&#1610;</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
  <span class=sf-dump-key>53</span> => <span class=sf-dump-note>array:13</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">nady</span>\"
    \"<span class=sf-dump-key>customer_data</span>\" => []
    \"<span class=sf-dump-key>client_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>total_hours</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>client_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>customer_amount_due</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>client_wells</span>\" => []
    \"<span class=sf-dump-key>customer_wells</span>\" => []
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-2079074366\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","filterStatus":"<pre class=sf-dump id=sf-dump-1968482073 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1968482073\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","separatedPayments":"<pre class=sf-dump id=sf-dump-395324443 data-indent-pad=\"  \"><span class=sf-dump-note>array:53</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1705;&#1608;&#1579;&#1585; &#1740;&#1608;&#1587;&#1601; &#1575;&#1581;&#1605;&#1583; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"8 characters\">14663.63</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"8 characters\">46084.93</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"6 characters\">150.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>60235.64</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">partial</span>\"
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"23 characters\">&#1580;&#1608;&#1607;&#1585;&#1577; &#1587;&#1604;&#1610;&#1605;&#1575;&#1606; &#1593;&#1576;&#1583; &#1575;&#1604;&#1603;&#1585;&#1610;&#1605;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"7 characters\">3594.59</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"8 characters\">14189.40</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"6 characters\">150.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>15995.41</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">partial</span>\"
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">&#1578;&#1575;&#1605;&#1585; &#1601;&#1575;&#1585;&#1608;&#1602; &#1605;&#1581;&#1605;&#1583; &#1587;&#1606;&#1608;&#1587;&#1610;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"8 characters\">11036.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"8 characters\">13386.03</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"6 characters\">150.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>24236.03</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">partial</span>\"
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">&#1588;&#1585;&#1610;&#1601; &#1593;&#1576;&#1583; &#1575;&#1604;&#1604;&#1607; &#1593;&#1604;&#1609; &#1587;&#1610;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"6 characters\">427.60</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1683.64</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"6 characters\">150.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>1683.64</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"
  </samp>]
  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>5</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1605;&#1607;&#1575; &#1581;&#1575;&#1605;&#1583; &#1605;&#1606;&#1589;&#1608;&#1585; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"6 characters\">756.96</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1903.21</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"6 characters\">150.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>2810.17</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>6</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">&#1571; &#1587;&#1593;&#1583; &#1605;&#1606;&#1589;&#1608;&#1585; &#1593;&#1576;&#1583; &#1575;&#1604;&#1587;&#1604;&#1575;&#1605;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"8 characters\">16698.72</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"8 characters\">11983.94</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"6 characters\">150.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>28832.66</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>7</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">&#1593;&#1604;&#1609; &#1593;&#1575;&#1588;&#1585; &#1593;&#1604;&#1609; &#1587;&#1610;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>8</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1606;&#1589;&#1585;&#1607; &#1576;&#1588;&#1606;&#1583;&#1610; &#1593;&#1591;&#1610;&#1577; &#1593;&#1608;&#1590;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"7 characters\">2822.16</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"8 characters\">10768.85</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"6 characters\">150.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>13741.01</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>9</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"23 characters\">&#1606;&#1580;&#1575;&#1577; &#1605;&#1606;&#1589;&#1608;&#1585; &#1587;&#1604;&#1610;&#1605;&#1575;&#1606; &#1605;&#1589;&#1591;&#1601;&#1609;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>10</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">&#1603;&#1605;&#1575;&#1604; &#1605;&#1581;&#1605;&#1583; &#1601;&#1604;&#1575;&#1581;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1593;&#1576;&#1610;&#1585; &#1586;&#1603;&#1609; &#1605;&#1581;&#1605;&#1583; &#1593;&#1605;&#1575;&#1585;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>12</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1585;&#1588;&#1575; &#1593;&#1576;&#1583; &#1575;&#1604;&#1604;&#1607; &#1573;&#1576;&#1585;&#1575;&#1607;&#1610;&#1605; &#1605;&#1606;&#1589;&#1608;&#1585;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>13</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1588;&#1610;&#1605;&#1575;&#1569; &#1593;&#1604;&#1609; &#1605;&#1581;&#1605;&#1583; &#1593;&#1604;&#1609;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>14</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"25 characters\">&#1740;&#1575;&#1587;&#1585; &#1605;&#1581;&#1605;&#1583; &#1593;&#1576;&#1583; &#1575;&#1604;&#1583;&#1575;&#1610;&#1605; &#1582;&#1605;&#1610;&#1587;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>15</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"25 characters\">&#1593;&#1576;&#1583; &#1575;&#1604;&#1606;&#1575;&#1589;&#1585; &#1575;&#1581;&#1605;&#1583; &#1606;&#1608;&#1585; &#1575;&#1604;&#1583;&#1610;&#1606;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>16</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1607;&#1575;&#1606;&#1740; &#1593;&#1576;&#1583; &#1575;&#1604;&#1604;&#1607; &#1585;&#1601;&#1575;&#1593;&#1740; &#1587;&#1604;&#1740;&#1605;&#1575;&#1606;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>17</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1575;&#1605;&#1606;&#1607; &#1593;&#1604;&#1609; &#1575;&#1581;&#1605;&#1583; &#1581;&#1587;&#1610;&#1606;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>18</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">&#1606;&#1608;&#1585; &#1571;&#1576;&#1608; &#1576;&#1603;&#1585; &#1605;&#1583;&#1606;&#1609; &#1593;&#1604;&#1609;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>19</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"29 characters\">&#1571;&#1588;&#1585;&#1601; &#1605;&#1581;&#1605;&#1608;&#1583; &#1593;&#1576;&#1583; &#1575;&#1604;&#1604;&#1607; &#1593;&#1576;&#1583; &#1575;&#1604;&#1606;&#1576;&#1610;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>20</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1589;&#1575;&#1576;&#1585; &#1605;&#1581;&#1605;&#1583; &#1605;&#1581;&#1605;&#1608;&#1583; &#1606;&#1589;&#1585;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>21</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1605;&#1583;&#1610;&#1581;&#1577; &#1581;&#1587;&#1610;&#1606; &#1593;&#1576;&#1583; &#1575;&#1604;&#1587;&#1604;&#1575;&#1605; &#1581;&#1587;&#1610;&#1606;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>22</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1587;&#1607;&#1575;&#1605; &#1605;&#1576;&#1575;&#1585;&#1586; &#1606;&#1608;&#1585; &#1575;&#1604;&#1583;&#1610;&#1606; &#1576;&#1603;&#1585;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>23</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"23 characters\">&#1610;&#1608;&#1587;&#1601; &#1573;&#1576;&#1585;&#1575;&#1607;&#1610;&#1605; &#1605;&#1581;&#1605;&#1583; &#1605;&#1576;&#1575;&#1585;&#1586;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>24</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">&#1571;&#1605;&#1575;&#1604; &#1601;&#1578;&#1581;&#1740; &#1605;&#1581;&#1605;&#1583; &#1575;&#1576;&#1585;&#1575;&#1607;&#1740;&#1605;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>25</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1605;&#1585;&#1601;&#1578; &#1593;&#1576;&#1583; &#1575;&#1604;&#1608;&#1607;&#1575;&#1576; &#1576;&#1583;&#1585;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>26</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"25 characters\">&#1607;&#1583;&#1609; &#1593;&#1576;&#1583; &#1575;&#1604;&#1587;&#1604;&#1575;&#1605; &#1605;&#1581;&#1605;&#1583; &#1605;&#1581;&#1605;&#1608;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>27</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"29 characters\">&#1573;&#1576;&#1585;&#1575;&#1607;&#1610;&#1605; &#1593;&#1576;&#1583; &#1575;&#1604;&#1608;&#1607;&#1575;&#1576; &#1575;&#1576;&#1608; &#1575;&#1604;&#1587;&#1593;&#1608;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>28</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1606;&#1585;&#1605;&#1740;&#1606; &#1705;&#1605;&#1575;&#1604; &#1605;&#1581;&#1605;&#1583; &#1587;&#1740;&#1583; &#1593;&#1740;&#1587;&#1740;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>29</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"23 characters\">&#1605;&#1606;&#1575;&#1604; &#1576;&#1588;&#1606;&#1583;&#1610; &#1575;&#1581;&#1605;&#1583; &#1575;&#1587;&#1605;&#1575;&#1593;&#1740;&#1604;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>30</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"25 characters\">&#1606;&#1583;&#1610;&#1607; &#1593;&#1576;&#1583; &#1575;&#1604;&#1605;&#1608;&#1604;&#1609; &#1576;&#1588;&#1606;&#1583;&#1610; &#1587;&#1610;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>31</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#1605;&#1581;&#1605;&#1583; &#1593;&#1604;&#1740; &#1605;&#1581;&#1605;&#1583; &#1593;&#1604;&#1609;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>32</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1604;&#1740;&#1604;&#1740; &#1587;&#1740;&#1583; &#1575;&#1581;&#1605;&#1583; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>33</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"31 characters\">&#1605;&#1581;&#1605;&#1583; &#1593;&#1576;&#1583; &#1575;&#1604;&#1583;&#1575;&#1610;&#1605; &#1605;&#1581;&#1605;&#1583; &#1593;&#1576;&#1583; &#1575;&#1604;&#1583;&#1575;&#1610;&#1605;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>34</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1606;&#1608;&#1585;&#1607; &#1605;&#1581;&#1605;&#1583; &#1582;&#1604;&#1740;&#1604; &#1575;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>35</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"25 characters\">&#1582;&#1604;&#1610;&#1604; &#1594;&#1575;&#1574;&#1605; &#1593;&#1576;&#1583; &#1575;&#1604;&#1581;&#1605;&#1610;&#1583; &#1575;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>36</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#1607;&#1583;&#1609; &#1581;&#1587;&#1606; &#1605;&#1581;&#1605;&#1583; &#1581;&#1587;&#1610;&#1606;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>37</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1578;&#1575;&#1605;&#1585; &#1587;&#1593;&#1610;&#1583; &#1610;&#1608;&#1606;&#1587; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>38</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1607;&#1606;&#1575;&#1569; &#1587;&#1610;&#1583; &#1605;&#1581;&#1605;&#1583; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>39</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1605;&#1581;&#1605;&#1583; &#1583;&#1585;&#1583;&#1740;&#1585; &#1605;&#1581;&#1605;&#1583; &#1593;&#1576;&#1583; &#1575;&#1604;&#1587;&#1604;&#1575;&#1605;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>40</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">&#1605;&#1587;&#1578;&#1608;&#1585;&#1577; &#1605;&#1607;&#1583;&#1610; &#1587;&#1610;&#1583; &#1575;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>41</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1589;&#1576;&#1581;&#1740; &#1605;&#1606;&#1589;&#1608;&#1585; &#1593;&#1576;&#1583; &#1575;&#1604;&#1587;&#1604;&#1575;&#1605; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>42</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1571;&#1581;&#1605;&#1583; &#1610;&#1608;&#1606;&#1587; &#1605;&#1581;&#1605;&#1583; &#1610;&#1608;&#1606;&#1587;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>43</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1589;&#1576;&#1575;&#1581; &#1581;&#1575;&#1605;&#1583; &#1605;&#1581;&#1605;&#1583; &#1582;&#1604;&#1610;&#1604;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>44</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1601;&#1575;&#1591;&#1605;&#1607; &#1587;&#1593;&#1583; &#1605;&#1581;&#1605;&#1583; &#1587;&#1593;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>45</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1571;&#1581;&#1605;&#1583; &#1581;&#1575;&#1605;&#1583; &#1605;&#1581;&#1605;&#1583; &#1582;&#1604;&#1610;&#1604;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>46</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1605;&#1605;&#1583;&#1608;&#1581; &#1605;&#1581;&#1605;&#1583; &#1593;&#1608;&#1590; &#1575;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>47</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">&#1589;&#1575;&#1576;&#1585; &#1581;&#1587;&#1740;&#1606; &#1590;&#1581;&#1575;&#1608;&#1740; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>48</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">&#1581;&#1604;&#1610;&#1605;&#1607; &#1593;&#1576;&#1583; &#1575;&#1604;&#1585;&#1581;&#1605;&#1606; &#1575;&#1581;&#1605;&#1583; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>49</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">&#1589;&#1575;&#1576;&#1585; &#1576;&#1588;&#1606;&#1583;&#1740; &#1581;&#1575;&#1601;&#1592;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>50</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#1606;&#1580;&#1575;&#1577; &#1606;&#1589;&#1585; &#1593;&#1605;&#1585; &#1605;&#1581;&#1605;&#1583;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>51</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">&#1607;&#1575;&#1583;&#1610;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>52</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">&#1601;&#1575;&#1583;&#1610;</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">main_client</span>\"
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>53</span>
    \"<span class=sf-dump-key>customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>client_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">nady</span>\"
    \"<span class=sf-dump-key>customer_name</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>amount_due</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>additional_paid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>active_bonus</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"
    \"<span class=sf-dump-key>remaining_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">unpaid</span>\"
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-395324443\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $data at D:\\laragon\\www\\Beer-Elshabab\\resources\\views\\invoices\\showInvoice.blade.php:132)
[stacktrace]
#0 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#1 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#3 D:\\laragon\\www\\Beer-Elshabab\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#4 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\Beer-Elshabab\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\AdminMiddleware.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\LastSeenMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\LastSeenMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\Localization.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\Beer-Elshabab\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\UpdateUserStatus.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UpdateUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\Beer-Elshabab\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $data at D:\\laragon\\www\\Beer-Elshabab\\storage\\framework\\views\\4144927bf7365069bb50a289a3134cea.php:166)
[stacktrace]
#0 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'D:\\\\laragon\\\\www\\\\...', 166)
#1 D:\\laragon\\www\\Beer-Elshabab\\storage\\framework\\views\\4144927bf7365069bb50a289a3134cea.php(166): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'D:\\\\laragon\\\\www\\\\...', 166)
#2 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\Beer-Elshabab\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\Beer-Elshabab\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#8 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#9 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#10 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#11 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#12 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\AdminMiddleware.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\LastSeenMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\LastSeenMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\Localization.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\Beer-Elshabab\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\UpdateUserStatus.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UpdateUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\Beer-Elshabab\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 {main}
"} 
[2025-07-17 23:37:34] local.INFO: Starting addToRegularInvoice {"invoice_reason_id":"8","request_data":{"_token":"CrFTaOOTcBRQZlNUIgOIOW8gjPA4FUOce4qcQTC7","regular_invoice_id":"386"}} 
[2025-07-17 23:37:34] local.INFO: Found invoice reason {"invoice_reason":{"id":8,"client_id":null,"total_amount_due":"100000.00","reason":"تيست","start_date":"2025-01-31T22:00:00.000000Z","end_date":"2025-07-23T22:00:00.000000Z","wells_config":"[{\"id\":\"1\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0643\\u0628\\u064a\\u0631\",\"total_hours\":\"1216.4833333333333\",\"percentage\":\"33.6\",\"allocated_amount\":\"33600.00\"},{\"id\":\"2\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0635\\u063a\\u064a\\u0631\",\"total_hours\":\"200.25\",\"percentage\":\"66.4\",\"allocated_amount\":\"66400.00\"}]","wells_hourly_rates":"{\"1\":27.620600364438477,\"2\":331.585518102372}","created_at":"2025-07-17T19:40:53.000000Z","updated_at":"2025-07-17T19:40:53.000000Z"}} 
[2025-07-17 23:37:35] local.INFO: Reason client data {"data":[{"client_id":1,"client_name":"کوثر یوسف احمد محمد","client_amount":10004.693519309743,"customers":[{"id":1,"name":"eslam","amount":25527.901606001353},{"id":2,"name":"hady","amount":11560.42488847318}]},{"client_id":2,"client_name":"جوهرة سليمان عبد الكريم","client_amount":13346.534695509645,"customers":[]},{"client_id":3,"client_name":"تامر فاروق محمد سنوسي","client_amount":13066.756992045164,"customers":[]},{"client_id":4,"client_name":"شريف عبد الله على سيد","client_amount":1713.1687912407372,"customers":[]},{"client_id":5,"client_name":"مها حامد منصور محمد","client_amount":1105.1006420357596,"customers":[]},{"client_id":6,"client_name":"أ سعد منصور عبد السلام","client_amount":12733.096768006137,"customers":[]},{"client_id":8,"client_name":"نصره بشندي عطية عوض","client_amount":10942.322097378277,"customers":[]}]} 
[2025-07-17 23:37:35] local.INFO: Regular invoice clients {"clients":{"1":true,"2":true,"3":true,"4":true,"5":true,"6":true,"8":true}} 
[2025-07-17 23:37:35] local.INFO: Applying amounts to regular invoice {"reason_client_data_count":7,"regular_invoice_clients_count":7} 
[2025-07-17 23:37:35] local.INFO: Processing client {"client_id":1,"client_name":"کوثر یوسف احمد محمد","has_hours_in_regular":true,"client_amount":10004.693519309743,"customers_count":2} 
[2025-07-17 23:37:36] local.INFO: Updated main client payment {"client_id":1,"invoice_id":"386","additional_amount_added":10004.693519309743,"new_additional_paid":56089.623519309745,"new_remaining_amount":70240.33351930974} 
[2025-07-17 23:37:37] local.INFO: Processing client {"client_id":2,"client_name":"جوهرة سليمان عبد الكريم","has_hours_in_regular":true,"client_amount":13346.534695509645,"customers_count":0} 
[2025-07-17 23:37:37] local.INFO: Updated main client payment {"client_id":2,"invoice_id":"386","additional_amount_added":13346.534695509645,"new_additional_paid":27535.934695509644,"new_remaining_amount":29341.944695509643} 
[2025-07-17 23:37:37] local.INFO: Processing client {"client_id":3,"client_name":"تامر فاروق محمد سنوسي","has_hours_in_regular":true,"client_amount":13066.756992045164,"customers_count":0} 
[2025-07-17 23:37:37] local.INFO: Updated main client payment {"client_id":3,"invoice_id":"386","additional_amount_added":13066.756992045164,"new_additional_paid":26452.786992045163,"new_remaining_amount":37302.78699204516} 
[2025-07-17 23:37:37] local.INFO: Processing client {"client_id":4,"client_name":"شريف عبد الله على سيد","has_hours_in_regular":true,"client_amount":1713.1687912407372,"customers_count":0} 
[2025-07-17 23:37:37] local.INFO: Updated main client payment {"client_id":4,"invoice_id":"386","additional_amount_added":1713.1687912407372,"new_additional_paid":3396.8087912407373,"new_remaining_amount":3396.8087912407373} 
[2025-07-17 23:37:37] local.INFO: Processing client {"client_id":5,"client_name":"مها حامد منصور محمد","has_hours_in_regular":true,"client_amount":1105.1006420357596,"customers_count":0} 
[2025-07-17 23:37:38] local.INFO: Updated main client payment {"client_id":5,"invoice_id":"386","additional_amount_added":1105.1006420357596,"new_additional_paid":3008.31064203576,"new_remaining_amount":3915.27064203576} 
[2025-07-17 23:37:38] local.INFO: Processing client {"client_id":6,"client_name":"أ سعد منصور عبد السلام","has_hours_in_regular":true,"client_amount":12733.096768006137,"customers_count":0} 
[2025-07-17 23:37:38] local.INFO: Updated main client payment {"client_id":6,"invoice_id":"386","additional_amount_added":12733.096768006137,"new_additional_paid":24717.03676800614,"new_remaining_amount":41565.75676800613} 
[2025-07-17 23:37:38] local.INFO: Processing client {"client_id":8,"client_name":"نصره بشندي عطية عوض","has_hours_in_regular":true,"client_amount":10942.322097378277,"customers_count":0} 
[2025-07-17 23:37:38] local.INFO: Updated main client payment {"client_id":8,"invoice_id":"386","additional_amount_added":10942.322097378277,"new_additional_paid":21711.17209737828,"new_remaining_amount":24683.332097378276} 
[2025-07-17 23:37:38] local.INFO: Finished applying amounts to regular invoice  
[2025-07-17 23:37:38] local.INFO: Successfully added amounts to regular invoice  
[2025-07-17 23:39:44] local.ERROR: Undefined array key "customer_hours" {"userId":1,"exception":"[object] (ErrorException(code: 0): Undefined array key \"customer_hours\" at D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Controllers\\InvoicesController.php:759)
[stacktrace]
#0 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined array...', 'D:\\\\laragon\\\\www\\\\...', 759)
#1 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Controllers\\InvoicesController.php(759): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined array...', 'D:\\\\laragon\\\\www\\\\...', 759)
#2 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\InvoicesController->showInvoice(Object(Illuminate\\Http\\Request), '386')
#3 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('showInvoice', Array)
#4 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\InvoicesController), 'showInvoice')
#5 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#7 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\AdminMiddleware.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\LastSeenMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\LastSeenMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\Localization.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\Beer-Elshabab\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\UpdateUserStatus.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UpdateUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\Beer-Elshabab\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 {main}
"} 
[2025-07-17 23:43:23] local.INFO: Hours fetched for well ID: 1 {"Start Date":"2025-02-16 00:00:00","End Date":"2025-03-15 23:59:59","Hours Details":[{"id":151,"client_id":1,"well_id":1,"start_time":"2025-02-27 19:59:00","end_time":"2025-02-17 12:00:00","added_by":1,"customer_id":1,"hour_price":"36.62","amount_due":"9081.27","created_at":"2025-02-27T17:58:24.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null},{"id":152,"client_id":2,"well_id":1,"start_time":"2025-02-25 19:58:00","end_time":"2025-02-26 12:00:00","added_by":1,"customer_id":null,"hour_price":"36.62","amount_due":"587.15","created_at":"2025-02-27T17:58:43.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null},{"id":153,"client_id":5,"well_id":1,"start_time":"2025-02-27 20:00:00","end_time":"2025-02-28 12:00:00","added_by":1,"customer_id":null,"hour_price":"36.62","amount_due":"585.93","created_at":"2025-02-27T18:00:38.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null},{"id":155,"client_id":1,"well_id":1,"start_time":"2025-02-28 12:00:00","end_time":"2025-03-01 12:00:00","added_by":1,"customer_id":1,"hour_price":"36.62","amount_due":"878.89","created_at":"2025-02-27T18:01:13.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null},{"id":156,"client_id":3,"well_id":1,"start_time":"2025-02-27 12:00:00","end_time":"2025-03-10 12:00:00","added_by":1,"customer_id":null,"hour_price":"36.62","amount_due":"9667.81","created_at":"2025-02-27T18:01:27.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null},{"id":157,"client_id":6,"well_id":1,"start_time":"2025-02-25 12:00:00","end_time":"2025-03-16 12:00:00","added_by":1,"customer_id":null,"hour_price":"36.62","amount_due":"16698.95","created_at":"2025-02-27T18:07:50.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null}],"Total Hours Worked":1024.0166666666667} 
[2025-07-17 23:43:23] local.INFO: Hours fetched for well ID: 2 {"Start Date":"2025-02-16 00:00:00","End Date":"2025-03-15 23:59:59","Hours Details":[{"id":154,"client_id":1,"well_id":2,"start_time":"2025-02-27 12:00:00","end_time":"2025-02-28 12:00:00","added_by":1,"customer_id":2,"hour_price":"85.52","amount_due":"2052.45","created_at":"2025-02-27T18:00:56.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null},{"id":165,"client_id":1,"well_id":2,"start_time":"2025-02-28 12:01:00","end_time":"2025-02-28 18:01:00","added_by":1,"customer_id":1,"hour_price":"85.52","amount_due":"513.11","created_at":"2025-03-05T09:13:49.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null},{"id":167,"client_id":2,"well_id":2,"start_time":"2025-02-28 18:02:00","end_time":"2025-03-01 00:02:00","added_by":1,"customer_id":null,"hour_price":"85.52","amount_due":"513.11","created_at":"2025-03-05T09:28:02.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null},{"id":169,"client_id":2,"well_id":2,"start_time":"2025-03-01 00:03:00","end_time":"2025-03-01 06:03:00","added_by":1,"customer_id":null,"hour_price":"85.52","amount_due":"513.11","created_at":"2025-03-05T09:31:59.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null},{"id":170,"client_id":3,"well_id":2,"start_time":"2025-03-01 06:04:00","end_time":"2025-03-01 12:04:00","added_by":1,"customer_id":null,"hour_price":"85.52","amount_due":"513.11","created_at":"2025-03-05T09:32:20.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null},{"id":172,"client_id":1,"well_id":2,"start_time":"2025-03-01 12:05:00","end_time":"2025-03-01 20:05:00","added_by":1,"customer_id":2,"hour_price":"85.52","amount_due":"684.15","created_at":"2025-03-05T09:41:04.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null},{"id":173,"client_id":1,"well_id":2,"start_time":"2025-03-01 20:06:00","end_time":"2025-03-02 03:06:00","added_by":1,"customer_id":1,"hour_price":"85.52","amount_due":"598.63","created_at":"2025-03-05T09:44:21.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null},{"id":178,"client_id":4,"well_id":2,"start_time":"2025-03-02 03:07:00","end_time":"2025-03-02 08:07:00","added_by":1,"customer_id":null,"hour_price":"85.52","amount_due":"427.59","created_at":"2025-03-05T10:03:40.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null},{"id":179,"client_id":3,"well_id":2,"start_time":"2025-03-02 08:08:00","end_time":"2025-03-02 13:08:00","added_by":1,"customer_id":null,"hour_price":"85.52","amount_due":"427.59","created_at":"2025-03-05T10:11:38.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null},{"id":182,"client_id":2,"well_id":2,"start_time":"2025-03-05 12:50:00","end_time":"2025-03-06 12:00:00","added_by":1,"customer_id":null,"hour_price":"85.52","amount_due":"1981.19","created_at":"2025-03-05T10:51:31.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null},{"id":184,"client_id":1,"well_id":2,"start_time":"2025-03-06 12:01:00","end_time":"2025-03-06 17:01:00","added_by":1,"customer_id":1,"hour_price":"85.52","amount_due":"427.59","created_at":"2025-03-05T10:56:23.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null},{"id":191,"client_id":8,"well_id":2,"start_time":"2025-03-12 13:17:00","end_time":"2025-03-13 18:17:00","added_by":1,"customer_id":null,"hour_price":"85.52","amount_due":"2480.05","created_at":"2025-03-06T11:19:48.000000Z","updated_at":"2025-06-18T21:48:32.000000Z","deleted_at":null},{"id":192,"client_id":8,"well_id":2,"start_time":"2025-03-13 18:18:00","end_time":"2025-03-13 22:18:00","added_by":1,"customer_id":null,"hour_price":"85.52","amount_due":"342.08","created_at":"2025-03-06T11:20:50.000000Z","updated_at":"2025-06-18T21:48:33.000000Z","deleted_at":null},{"id":194,"client_id":3,"well_id":2,"start_time":"2025-03-13 22:19:00","end_time":"2025-03-14 03:19:00","added_by":1,"customer_id":null,"hour_price":"85.52","amount_due":"427.59","created_at":"2025-03-06T11:29:02.000000Z","updated_at":"2025-06-18T21:48:33.000000Z","deleted_at":null},{"id":197,"client_id":1,"well_id":2,"start_time":"2025-03-14 03:20:00","end_time":"2025-03-14 05:20:00","added_by":1,"customer_id":1,"hour_price":"85.52","amount_due":"171.04","created_at":"2025-03-06T11:32:31.000000Z","updated_at":"2025-06-18T21:48:33.000000Z","deleted_at":null},{"id":199,"client_id":5,"well_id":2,"start_time":"2025-03-14 05:21:00","end_time":"2025-03-14 07:21:00","added_by":1,"customer_id":null,"hour_price":"85.52","amount_due":"171.04","created_at":"2025-03-06T11:37:16.000000Z","updated_at":"2025-06-18T21:48:33.000000Z","deleted_at":null},{"id":200,"client_id":1,"well_id":2,"start_time":"2025-03-14 07:22:00","end_time":"2025-03-14 10:22:00","added_by":1,"customer_id":null,"hour_price":"85.52","amount_due":"256.56","created_at":"2025-03-23T14:15:12.000000Z","updated_at":"2025-06-18T21:48:33.000000Z","deleted_at":null}],"Total Hours Worked":146.16666666666669} 
[2025-07-17 23:43:39] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.1] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "e991d4e0-8e86-4441-ada4-8d3de98ae5c7",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:39] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.2] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "37b60b5c-3aee-4885-a114-8406dc6e6258",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:39] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.3] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "794eab26-38d1-47b4-8c4f-520904da3ba2",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:39] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.4] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "236ac171-199b-40e2-ac66-db3e4ad5f1bc",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:39] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.5] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "1ae16f8e-29c9-4a03-bd68-d2360eafcad2",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:39] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.6] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "e03a59b2-d1b2-4268-a330-137332b21363",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:40] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.7] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "5bbcddc8-e9c9-41c1-8546-fefb7559a0ab",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:40] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.8] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "e440894b-35ed-4c0a-a6cc-bb9e7ea0b688",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:40] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.9] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "188f83aa-f494-4813-8820-873a1f069f94",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:40] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.10] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "b683ef38-fa0e-486b-b61f-c6ab25a9484a",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:40] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.11] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "78ee7e0b-0a82-479c-9840-810967a7be78",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:41] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.12] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "b9c339cf-18a4-4781-aed2-eed5756bf477",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:41] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.13] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "acd563c5-6cfe-4e67-8603-a95c649d5eff",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:41] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.14] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "c2193a44-232b-4628-8eef-fc5d7d21a695",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:41] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.15] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "95c60bc3-ba97-48ea-ba92-81857b6fec86",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:41] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.16] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "9c66cbe8-accf-4add-bec3-84c51a0dd269",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:41] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.17] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "6d714fe8-aafd-455a-8943-17c3cd0cebcc",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:41] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.18] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "f484fe98-4b96-4ccd-a2b9-2bcd61b8566c",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:41] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.19] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "d08ff3c7-b9cf-45ab-8551-5a5c03b9cee3",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:41] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.20] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "fba46431-c44b-4c6e-b0d8-9d2aac573016",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:41] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.21] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "97e90c29-09ad-4afb-b91e-c70f8482da69",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:41] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.22] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "bcc671d4-8520-4b8c-8e40-93f9e7343980",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:42] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.23] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "56115536-c9a2-4500-9db2-891fe586be68",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:42] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.24] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "2c2ec863-47fc-4a1d-b4ef-4b979fd10f18",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:42] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.25] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "b5703b32-72e9-4c6e-9f42-81beaef0101b",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:42] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.26] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "00cee78d-0629-4232-9981-ac4d092d7796",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:42] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.27] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "9c63f17a-33b1-4421-8a91-06185f6dc662",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:42] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.28] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "d7ddbf8c-b364-433f-bd70-3f08c82aa6be",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:42] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.29] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "d401cc12-34a3-482b-a3f9-c93d0dfafa10",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:42] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.30] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "90110831-54df-49bc-9b7a-cf55fa686a6b",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:43] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.31] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "0914c968-42d1-4b02-93de-2930cdae2346",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:43] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.32] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "76472ab3-6e1f-4721-8d10-5c8bbce777d4",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:43] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.33] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "1f5ad8a4-ca77-4687-a13c-e21d7bdaa16f",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:43] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.34] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "63334f7b-7b98-4cd9-9167-f643492a61a9",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:43] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.35] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "1317eec7-dd9e-483e-bcad-6bb2e73b7ba3",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:43] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.36] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "8a3e9f2e-7d04-4758-8125-4f2d4719a862",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:43] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.37] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "da1b5443-04d5-4348-a0df-a129c0b5e8ca",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:43] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.38] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "07276485-a152-4c60-b20c-01a31bec5a0d",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:43] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.39] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "a94c912b-7021-4bee-9696-417619b535a7",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:43] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.40] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "a29991ae-6aad-4a09-a49e-138deec43ab5",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:43] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.41] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "d81c0918-a1d5-47eb-acef-060b46723bc7",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:43] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.42] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "0289908d-817d-465f-b745-68d825064995",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:43] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.43] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "e3d0ad12-067a-41b3-aa57-d4c58f38dea6",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:43] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.44] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "ec6a7632-9b10-4ba8-bf76-a95ef8241a32",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:44] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.45] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "e080f1f0-5cdf-4bff-a9df-aaa5520d5849",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:44] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.46] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "995a03ed-7a9f-4b8f-91b4-c6632822a693",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:44] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.47] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "e56569cc-99ed-4fa2-b733-305c3ae86b14",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:44] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.48] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "7408e033-2474-4cbf-ac97-d22e0a31152d",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:44] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.49] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "48ed5db1-f93b-4936-a1ea-88dc8cf64742",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:44] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.50] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "28308d27-d02c-419b-bab8-8e5d1ea9fcad",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:44] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.51] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "f2cbc906-c3e1-4b35-842b-2f360db207e9",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:44] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.52] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "e17c16a3-75d7-46cf-a13a-9f9be1d14c29",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-17 23:43:44] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.53] with payload:
{
    "message": {
        "en": "Your invoice for March 2025 has been created.",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 387,
    "month": "2025-03-01",
    "id": "44d6ff7d-40ec-4d0e-acd0-3f95e1599f2b",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:49] local.INFO: Hours fetched for well ID: 1 {"Start Date":"2025-02-16 00:00:00","End Date":"2025-03-15 23:59:59","Hours Details":[{"id":151,"client_id":1,"well_id":1,"start_time":"2025-02-27 19:59:00","end_time":"2025-02-17 12:00:00","added_by":1,"customer_id":1,"hour_price":"33.57","amount_due":"8324.50","created_at":"2025-02-27T17:58:24.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":152,"client_id":2,"well_id":1,"start_time":"2025-02-25 19:58:00","end_time":"2025-02-26 12:00:00","added_by":1,"customer_id":null,"hour_price":"33.57","amount_due":"538.22","created_at":"2025-02-27T17:58:43.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":153,"client_id":5,"well_id":1,"start_time":"2025-02-27 20:00:00","end_time":"2025-02-28 12:00:00","added_by":1,"customer_id":null,"hour_price":"33.57","amount_due":"537.10","created_at":"2025-02-27T18:00:38.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":155,"client_id":1,"well_id":1,"start_time":"2025-02-28 12:00:00","end_time":"2025-03-01 12:00:00","added_by":1,"customer_id":1,"hour_price":"33.57","amount_due":"805.65","created_at":"2025-02-27T18:01:13.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":156,"client_id":3,"well_id":1,"start_time":"2025-02-27 12:00:00","end_time":"2025-03-10 12:00:00","added_by":1,"customer_id":null,"hour_price":"33.57","amount_due":"8862.16","created_at":"2025-02-27T18:01:27.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":157,"client_id":6,"well_id":1,"start_time":"2025-02-25 12:00:00","end_time":"2025-03-16 12:00:00","added_by":1,"customer_id":null,"hour_price":"33.57","amount_due":"15307.37","created_at":"2025-02-27T18:07:50.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null}],"Total Hours Worked":1024.0166666666667} 
[2025-07-18 00:12:49] local.INFO: Hours fetched for well ID: 2 {"Start Date":"2025-02-16 00:00:00","End Date":"2025-03-15 23:59:59","Hours Details":[{"id":154,"client_id":1,"well_id":2,"start_time":"2025-02-27 12:00:00","end_time":"2025-02-28 12:00:00","added_by":1,"customer_id":2,"hour_price":"106.90","amount_due":"2565.56","created_at":"2025-02-27T18:00:56.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":165,"client_id":1,"well_id":2,"start_time":"2025-02-28 12:01:00","end_time":"2025-02-28 18:01:00","added_by":1,"customer_id":1,"hour_price":"106.90","amount_due":"641.39","created_at":"2025-03-05T09:13:49.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":167,"client_id":2,"well_id":2,"start_time":"2025-02-28 18:02:00","end_time":"2025-03-01 00:02:00","added_by":1,"customer_id":null,"hour_price":"106.90","amount_due":"641.39","created_at":"2025-03-05T09:28:02.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":169,"client_id":2,"well_id":2,"start_time":"2025-03-01 00:03:00","end_time":"2025-03-01 06:03:00","added_by":1,"customer_id":null,"hour_price":"106.90","amount_due":"641.39","created_at":"2025-03-05T09:31:59.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":170,"client_id":3,"well_id":2,"start_time":"2025-03-01 06:04:00","end_time":"2025-03-01 12:04:00","added_by":1,"customer_id":null,"hour_price":"106.90","amount_due":"641.39","created_at":"2025-03-05T09:32:20.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":172,"client_id":1,"well_id":2,"start_time":"2025-03-01 12:05:00","end_time":"2025-03-01 20:05:00","added_by":1,"customer_id":2,"hour_price":"106.90","amount_due":"855.19","created_at":"2025-03-05T09:41:04.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":173,"client_id":1,"well_id":2,"start_time":"2025-03-01 20:06:00","end_time":"2025-03-02 03:06:00","added_by":1,"customer_id":1,"hour_price":"106.90","amount_due":"748.29","created_at":"2025-03-05T09:44:21.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":178,"client_id":4,"well_id":2,"start_time":"2025-03-02 03:07:00","end_time":"2025-03-02 08:07:00","added_by":1,"customer_id":null,"hour_price":"106.90","amount_due":"534.49","created_at":"2025-03-05T10:03:40.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":179,"client_id":3,"well_id":2,"start_time":"2025-03-02 08:08:00","end_time":"2025-03-02 13:08:00","added_by":1,"customer_id":null,"hour_price":"106.90","amount_due":"534.49","created_at":"2025-03-05T10:11:38.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":182,"client_id":2,"well_id":2,"start_time":"2025-03-05 12:50:00","end_time":"2025-03-06 12:00:00","added_by":1,"customer_id":null,"hour_price":"106.90","amount_due":"2476.48","created_at":"2025-03-05T10:51:31.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":184,"client_id":1,"well_id":2,"start_time":"2025-03-06 12:01:00","end_time":"2025-03-06 17:01:00","added_by":1,"customer_id":1,"hour_price":"106.90","amount_due":"534.49","created_at":"2025-03-05T10:56:23.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":191,"client_id":8,"well_id":2,"start_time":"2025-03-12 13:17:00","end_time":"2025-03-13 18:17:00","added_by":1,"customer_id":null,"hour_price":"106.90","amount_due":"3100.06","created_at":"2025-03-06T11:19:48.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":192,"client_id":8,"well_id":2,"start_time":"2025-03-13 18:18:00","end_time":"2025-03-13 22:18:00","added_by":1,"customer_id":null,"hour_price":"106.90","amount_due":"427.59","created_at":"2025-03-06T11:20:50.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":194,"client_id":3,"well_id":2,"start_time":"2025-03-13 22:19:00","end_time":"2025-03-14 03:19:00","added_by":1,"customer_id":null,"hour_price":"106.90","amount_due":"534.49","created_at":"2025-03-06T11:29:02.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":197,"client_id":1,"well_id":2,"start_time":"2025-03-14 03:20:00","end_time":"2025-03-14 05:20:00","added_by":1,"customer_id":1,"hour_price":"106.90","amount_due":"213.80","created_at":"2025-03-06T11:32:31.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":199,"client_id":5,"well_id":2,"start_time":"2025-03-14 05:21:00","end_time":"2025-03-14 07:21:00","added_by":1,"customer_id":null,"hour_price":"106.90","amount_due":"213.80","created_at":"2025-03-06T11:37:16.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null},{"id":200,"client_id":1,"well_id":2,"start_time":"2025-03-14 07:22:00","end_time":"2025-03-14 10:22:00","added_by":1,"customer_id":null,"hour_price":"106.90","amount_due":"320.70","created_at":"2025-03-23T14:15:12.000000Z","updated_at":"2025-07-17T21:43:23.000000Z","deleted_at":null}],"Total Hours Worked":146.16666666666669} 
[2025-07-18 00:12:52] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.1] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "9c8734c4-31a9-4685-94ec-4b1fe2944048",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:52] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.2] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "efab18ee-0173-4edc-9624-a7e439ebb1c8",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:52] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.3] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "231b85b8-670d-4e26-9031-48f9ed6dae11",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:52] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.4] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "fef34c09-a4ad-477e-8211-20d6db7138f8",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:52] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.5] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "71e428d0-672c-41a0-a4f5-b2ad2bd9d653",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:52] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.6] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "7fef9d36-3366-4eb8-a394-d2d017f2cd1a",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:53] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.7] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "22ce1d6d-30d6-4ffe-93ef-172f3ace8c55",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:53] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.8] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "14a3ffc1-747a-4d1d-9b46-6a1a3f3606ed",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:53] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.9] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "fd0f04d1-c188-479b-9f38-e0f3f46ea654",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:53] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.10] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "7c8a889a-1d8d-4e90-906e-7de0037d3dec",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:53] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.11] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "552e1b41-9e1b-4499-94e0-928ee826dbe0",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:53] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.12] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "9bbd66e6-fa53-43f3-bb0c-eda93470a3f4",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:53] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.13] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "fcefe119-e358-4954-b06b-7e4ae262e1b3",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:53] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.14] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "caa2940b-86d8-4892-839e-362781adda3f",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:53] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.15] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "3ce985c4-adc4-4563-9ebb-d25d30c314ec",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:53] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.16] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "982e79f0-fd41-425f-8414-bef4311f9ee6",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:54] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.17] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "898af16a-c423-42dc-b2a9-119021aba328",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:54] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.18] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "70329edf-0487-406f-b31f-f2ecbbb80fde",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:54] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.19] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "c99f61ca-183b-42a2-9fc9-d1ad771410bc",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:54] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.20] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "783e2608-11c0-4912-919b-06bc53507232",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:54] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.21] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "89ff29d8-6faa-4458-84b6-a1bc5c176f45",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:54] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.22] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "b34d99a0-83de-4810-bbd5-eca8dedfea47",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:54] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.23] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "dfe22a0b-6493-4236-96db-5892967f8380",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:54] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.24] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "98683623-7526-4b69-a887-68569953864d",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:54] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.25] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "2bcf95a5-f1f3-48f0-96e8-4a62a063c4d6",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:54] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.26] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "4daa5f73-1b9a-4a0f-b7a1-b9969b449644",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:54] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.27] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "610070c3-6ad2-4194-8e4f-50d7d6e16bd2",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:54] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.28] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "5cca74fa-693d-480a-bd74-d36cb2b9d7c9",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:54] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.29] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "f4c22ff6-5f6b-4fa7-bc65-a6cd7e814007",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:55] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.30] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "df64c82f-d824-4703-b0c6-5b60f5cca03b",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:55] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.31] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "ce8611e9-3078-40ce-9110-d03025d35275",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:55] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.32] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "05f18371-8eac-4561-8832-b3dfc51005aa",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:55] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.33] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "d2d8b839-1c1f-4755-900c-7eb006a3c7b5",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:55] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.34] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "067f12d5-1482-4c89-b04d-06152f77f02f",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:55] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.35] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "42fa4bc5-f0a7-4427-8798-843e6a227033",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:55] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.36] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "dacac52a-2d5d-479f-bc7f-46a3ef404f03",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:55] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.37] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "fbc9f753-e930-416b-aa71-91ce163eb40a",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:55] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.38] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "63173dea-c2d6-47e6-aa7e-c82800cda937",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:55] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.39] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "a2b27810-2fc9-4bf0-93db-9bde8888a878",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:55] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.40] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "8935ca13-3196-49a9-98d8-95e66b158c12",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:56] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.41] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "c5335b04-7a3f-46c0-a276-db318f2b082a",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:56] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.42] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "7813fa7c-55db-484a-88bd-f03638d8e1d0",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:56] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.43] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "74ee980d-3c31-474f-9b48-64f29d6d6759",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:56] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.44] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "1262c85f-3302-454f-b491-6e2d81f2fe73",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:56] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.45] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "9e50722c-3265-4959-9c5c-d82411299312",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:56] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.46] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "3ac2afad-2f73-4e82-bc3c-965315c135b8",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:56] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.47] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "718463d2-9917-47ce-bf02-84e08b51830e",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:56] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.48] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "62cab261-a265-45a9-8760-d3d51272ba0a",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:56] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.49] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "5e1ce0ca-8cd9-4899-b64e-d00c5e8852f8",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:56] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.50] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "43cfb2e2-a0cc-41e2-b873-6a9ea42ed66b",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:57] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.51] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "cd0f7ea8-8bdb-43c8-8d55-83fb69d0f2c7",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:57] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.52] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "5406410f-6b7b-4045-9eac-3f0ce407858d",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
[2025-07-18 00:12:57] local.INFO: Broadcasting [Illuminate\Notifications\Events\BroadcastNotificationCreated] on channels [private-App.Models.Client.53] with payload:
{
    "message": {
        "en": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 March 2025",
        "ae": ".\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0641\u0627\u062a\u0648\u0631\u062a\u0643 \u0644\u0634\u0647\u0631 \u0645\u0627\u0631\u0633 \u0662\u0660\u0662\u0665"
    },
    "invoice_id": 388,
    "month": "2025-03-01",
    "id": "04e58d42-cee9-4c12-a73b-2d50b993531d",
    "type": "App\\Notifications\\InvoiceCreatedNotification",
    "socket": null
}  
