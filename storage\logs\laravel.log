[2025-07-17 22:43:29] local.ERROR: Error adding to regular invoice: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 (Connection: mysql, SQL: insert into `client_balances` (`client_id`, `customer_id`, `invoice_reason_id`, `amount`, `status`, `description`, `updated_at`, `created_at`) values (1, ?, 0, -10008.*********, applied_to_invoice, مبلغ إضافي مطبق على الفاتورة العادية رقم 383, 2025-07-17 22:43:29, 2025-07-17 22:43:29))  
[2025-07-17 22:43:44] local.ERROR: Error adding to regular invoice: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 (Connection: mysql, SQL: insert into `client_balances` (`client_id`, `customer_id`, `invoice_reason_id`, `amount`, `status`, `description`, `updated_at`, `created_at`) values (1, ?, 0, -10008.*********, applied_to_invoice, مبلغ إضافي مطبق على الفاتورة العادية رقم 386, 2025-07-17 22:43:44, 2025-07-17 22:43:44))  
[2025-07-17 22:48:21] local.ERROR: Error adding to regular invoice: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`beer-el-shabab`.`client_balances`, CONSTRAINT `client_balances_invoice_reason_id_foreign` FOREIGN KEY (`invoice_reason_id`) REFERENCES `invoice_reasons` (`id`) ON DELETE CASCADE) (Connection: mysql, SQL: insert into `client_balances` (`client_id`, `customer_id`, `invoice_reason_id`, `amount`, `status`, `description`, `updated_at`, `created_at`) values (1, ?, 999999, -10008.*********, active, مبلغ إضافي من فاتورة أسباب مطبق على الفاتورة العادية رقم 386, 2025-07-17 22:48:20, 2025-07-17 22:48:20))  
[2025-07-17 22:48:55] local.ERROR: Error adding to regular invoice: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`beer-el-shabab`.`client_balances`, CONSTRAINT `client_balances_invoice_reason_id_foreign` FOREIGN KEY (`invoice_reason_id`) REFERENCES `invoice_reasons` (`id`) ON DELETE CASCADE) (Connection: mysql, SQL: insert into `client_balances` (`client_id`, `customer_id`, `invoice_reason_id`, `amount`, `status`, `description`, `updated_at`, `created_at`) values (1, ?, 999999, -10008.*********, active, مبلغ إضافي من فاتورة أسباب مطبق على الفاتورة العادية رقم 381, 2025-07-17 22:48:55, 2025-07-17 22:48:55))  
[2025-07-17 22:52:18] local.INFO: Starting addToRegularInvoice {"invoice_reason_id":"9","request_data":{"_token":"CrFTaOOTcBRQZlNUIgOIOW8gjPA4FUOce4qcQTC7","regular_invoice_id":"386"}} 
[2025-07-17 22:52:18] local.INFO: Found invoice reason {"invoice_reason":{"id":9,"client_id":null,"total_amount_due":"100000.00","reason":"تيست","start_date":"2024-12-31T22:00:00.000000Z","end_date":"2025-11-16T22:00:00.000000Z","wells_config":"[{\"id\":\"1\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0643\\u0628\\u064a\\u0631\",\"total_hours\":\"1307.9166666666667\",\"percentage\":\"34\",\"allocated_amount\":\"34000.00\"},{\"id\":\"2\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0635\\u063a\\u064a\\u0631\",\"total_hours\":\"202.25\",\"percentage\":\"66\",\"allocated_amount\":\"66000.00\"}]","wells_hourly_rates":"{\"1\":25.99553998088563,\"2\":326.3288009888752}","created_at":"2025-07-17T19:46:16.000000Z","updated_at":"2025-07-17T19:46:16.000000Z"}} 
[2025-07-17 22:52:18] local.INFO: Reason client data {"data":[{"client_id":1,"client_name":"کوثر یوسف احمد محمد","client_amount":10008.838760818775,"customers":[{"id":1,"name":"eslam","amount":24739.760271019204},{"id":2,"name":"hady","amount":11336.33494798679}]},{"client_id":2,"client_name":"جوهرة سليمان عبد الكريم","client_amount":14189.397283113554,"customers":[]},{"client_id":3,"client_name":"تامر فاروق محمد سنوسي","client_amount":13386.026664818497,"customers":[]},{"client_id":4,"client_name":"شريف عبد الله على سيد","client_amount":1683.635084906147,"customers":[]},{"client_id":5,"client_name":"مها حامد منصور محمد","client_amount":1903.2126235158703,"customers":[]},{"client_id":6,"client_name":"أ سعد منصور عبد السلام","client_amount":11983.943931188276,"customers":[]},{"client_id":8,"client_name":"نصره بشندي عطية عوض","client_amount":10768.85043263288,"customers":[]}]} 
[2025-07-17 22:52:18] local.INFO: Regular invoice clients {"clients":{"1":true,"2":true,"3":true,"4":true,"5":true,"6":true,"8":true}} 
[2025-07-17 22:52:18] local.INFO: Applying amounts to regular invoice {"reason_client_data_count":7,"regular_invoice_clients_count":7} 
[2025-07-17 22:52:18] local.INFO: Processing client {"client_id":1,"client_name":"کوثر یوسف احمد محمد","has_hours_in_regular":true,"client_amount":10008.838760818775,"customers_count":2} 
[2025-07-17 22:52:18] local.ERROR: Error adding to regular invoice {"message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`beer-el-shabab`.`client_balances`, CONSTRAINT `client_balances_invoice_reason_id_foreign` FOREIGN KEY (`invoice_reason_id`) REFERENCES `invoice_reasons` (`id`) ON DELETE CASCADE) (Connection: mysql, SQL: insert into `client_balances` (`client_id`, `customer_id`, `invoice_reason_id`, `amount`, `status`, `description`, `updated_at`, `created_at`) values (1, ?, 999999, -10008.*********, active, مبلغ إضافي من فاتورة أسباب مطبق على الفاتورة العادية رقم 386, 2025-07-17 22:52:18, 2025-07-17 22:52:18))","file":"D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":829,"trace":"#0 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `cl...', Array, Object(Closure))
#1 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('insert into `cl...', Array, Object(Closure))
#2 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(540): Illuminate\\Database\\Connection->statement('insert into `cl...', Array)
#3 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `cl...', Array)
#4 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3507): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `cl...', Array, 'id')
#5 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1333): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1298): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1137): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\ClientReasonBalance))
#11 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1026): tap(Object(App\\Models\\ClientReasonBalance), Object(Closure))
#12 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2334): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2346): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Controllers\\InvoiceReasonController.php(1389): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Controllers\\InvoiceReasonController.php(1342): App\\Http\\Controllers\\InvoiceReasonController->addAdditionalAmountToInvoice('386', 1, 10008.*********, Array)
#17 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Controllers\\InvoiceReasonController.php(1176): App\\Http\\Controllers\\InvoiceReasonController->applyAmountsToRegularInvoice(Array, Array, '386', '9')
#18 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\InvoiceReasonController->addToRegularInvoice('9', Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('addToRegularInv...', Array)
#20 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\InvoiceReasonController), 'addToRegularInv...')
#21 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#22 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#23 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\AdminMiddleware.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\LastSeenMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\LastSeenMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\Localization.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\Beer-Elshabab\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\UpdateUserStatus.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UpdateUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\Beer-Elshabab\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#72 {main}"} 
[2025-07-17 22:55:39] local.INFO: Starting addToRegularInvoice {"invoice_reason_id":"9","request_data":{"_token":"CrFTaOOTcBRQZlNUIgOIOW8gjPA4FUOce4qcQTC7","regular_invoice_id":"386"}} 
[2025-07-17 22:55:39] local.INFO: Found invoice reason {"invoice_reason":{"id":9,"client_id":null,"total_amount_due":"100000.00","reason":"تيست","start_date":"2024-12-31T22:00:00.000000Z","end_date":"2025-11-16T22:00:00.000000Z","wells_config":"[{\"id\":\"1\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0643\\u0628\\u064a\\u0631\",\"total_hours\":\"1307.9166666666667\",\"percentage\":\"34\",\"allocated_amount\":\"34000.00\"},{\"id\":\"2\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0635\\u063a\\u064a\\u0631\",\"total_hours\":\"202.25\",\"percentage\":\"66\",\"allocated_amount\":\"66000.00\"}]","wells_hourly_rates":"{\"1\":25.99553998088563,\"2\":326.3288009888752}","created_at":"2025-07-17T19:46:16.000000Z","updated_at":"2025-07-17T19:46:16.000000Z"}} 
[2025-07-17 22:55:39] local.INFO: Reason client data {"data":[{"client_id":1,"client_name":"کوثر یوسف احمد محمد","client_amount":10008.838760818775,"customers":[{"id":1,"name":"eslam","amount":24739.760271019204},{"id":2,"name":"hady","amount":11336.33494798679}]},{"client_id":2,"client_name":"جوهرة سليمان عبد الكريم","client_amount":14189.397283113554,"customers":[]},{"client_id":3,"client_name":"تامر فاروق محمد سنوسي","client_amount":13386.026664818497,"customers":[]},{"client_id":4,"client_name":"شريف عبد الله على سيد","client_amount":1683.635084906147,"customers":[]},{"client_id":5,"client_name":"مها حامد منصور محمد","client_amount":1903.2126235158703,"customers":[]},{"client_id":6,"client_name":"أ سعد منصور عبد السلام","client_amount":11983.943931188276,"customers":[]},{"client_id":8,"client_name":"نصره بشندي عطية عوض","client_amount":10768.85043263288,"customers":[]}]} 
[2025-07-17 22:55:39] local.INFO: Regular invoice clients {"clients":{"1":true,"2":true,"3":true,"4":true,"5":true,"6":true,"8":true}} 
[2025-07-17 22:55:39] local.INFO: Applying amounts to regular invoice {"reason_client_data_count":7,"regular_invoice_clients_count":7} 
[2025-07-17 22:55:39] local.INFO: Processing client {"client_id":1,"client_name":"کوثر یوسف احمد محمد","has_hours_in_regular":true,"client_amount":10008.838760818775,"customers_count":2} 
[2025-07-17 22:55:40] local.INFO: Processing client {"client_id":2,"client_name":"جوهرة سليمان عبد الكريم","has_hours_in_regular":true,"client_amount":14189.397283113554,"customers_count":0} 
[2025-07-17 22:55:40] local.INFO: Processing client {"client_id":3,"client_name":"تامر فاروق محمد سنوسي","has_hours_in_regular":true,"client_amount":13386.026664818497,"customers_count":0} 
[2025-07-17 22:55:40] local.INFO: Processing client {"client_id":4,"client_name":"شريف عبد الله على سيد","has_hours_in_regular":true,"client_amount":1683.635084906147,"customers_count":0} 
[2025-07-17 22:55:40] local.INFO: Processing client {"client_id":5,"client_name":"مها حامد منصور محمد","has_hours_in_regular":true,"client_amount":1903.2126235158703,"customers_count":0} 
[2025-07-17 22:55:40] local.INFO: Processing client {"client_id":6,"client_name":"أ سعد منصور عبد السلام","has_hours_in_regular":true,"client_amount":11983.943931188276,"customers_count":0} 
[2025-07-17 22:55:40] local.INFO: Processing client {"client_id":8,"client_name":"نصره بشندي عطية عوض","has_hours_in_regular":true,"client_amount":10768.85043263288,"customers_count":0} 
[2025-07-17 22:55:40] local.INFO: Finished applying amounts to regular invoice  
[2025-07-17 22:55:40] local.ERROR: Error adding to regular invoice {"message":"SQLSTATE[01000]: Warning: 1265 Data truncated for column 'status' at row 1 (Connection: mysql, SQL: insert into `client_balances` (`client_id`, `customer_id`, `invoice_reason_id`, `amount`, `status`, `description`, `updated_at`, `created_at`) values (1, ?, 9, 0, processed, تم تطبيق فاتورة الأسباب على الفاتورة العادية رقم 386, 2025-07-17 22:55:40, 2025-07-17 22:55:40))","file":"D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":829,"trace":"#0 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `cl...', Array, Object(Closure))
#1 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('insert into `cl...', Array, Object(Closure))
#2 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(540): Illuminate\\Database\\Connection->statement('insert into `cl...', Array)
#3 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `cl...', Array)
#4 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3507): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `cl...', Array, 'id')
#5 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1333): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1298): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1137): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\ClientReasonBalance))
#11 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1026): tap(Object(App\\Models\\ClientReasonBalance), Object(Closure))
#12 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2334): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2346): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Controllers\\InvoiceReasonController.php(1185): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\InvoiceReasonController->addToRegularInvoice('9', Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('addToRegularInv...', Array)
#18 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\InvoiceReasonController), 'addToRegularInv...')
#19 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#20 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#21 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\AdminMiddleware.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\LastSeenMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\LastSeenMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\Localization.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\Beer-Elshabab\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\Beer-Elshabab\\app\\Http\\Middleware\\UpdateUserStatus.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UpdateUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 D:\\laragon\\www\\Beer-Elshabab\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\Beer-Elshabab\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 {main}"} 
[2025-07-17 22:58:17] local.INFO: Starting addToRegularInvoice {"invoice_reason_id":"11","request_data":{"_token":"CrFTaOOTcBRQZlNUIgOIOW8gjPA4FUOce4qcQTC7","regular_invoice_id":"386"}} 
[2025-07-17 22:58:17] local.INFO: Found invoice reason {"invoice_reason":{"id":11,"client_id":null,"total_amount_due":"100000.00","reason":"تيست","start_date":"2024-12-31T22:00:00.000000Z","end_date":"2025-11-16T22:00:00.000000Z","wells_config":"[{\"id\":\"1\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0643\\u0628\\u064a\\u0631\",\"total_hours\":\"1307.9166666666667\",\"percentage\":\"34\",\"allocated_amount\":\"34000.00\"},{\"id\":\"2\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0635\\u063a\\u064a\\u0631\",\"total_hours\":\"202.25\",\"percentage\":\"66\",\"allocated_amount\":\"66000.00\"}]","wells_hourly_rates":"{\"1\":25.99553998088563,\"2\":326.3288009888752}","created_at":"2025-07-17T19:52:13.000000Z","updated_at":"2025-07-17T19:52:13.000000Z"}} 
[2025-07-17 22:58:17] local.INFO: Reason client data {"data":[{"client_id":1,"client_name":"کوثر یوسف احمد محمد","client_amount":10008.838760818775,"customers":[{"id":1,"name":"eslam","amount":24739.760271019204},{"id":2,"name":"hady","amount":11336.33494798679}]},{"client_id":2,"client_name":"جوهرة سليمان عبد الكريم","client_amount":14189.397283113554,"customers":[]},{"client_id":3,"client_name":"تامر فاروق محمد سنوسي","client_amount":13386.026664818497,"customers":[]},{"client_id":4,"client_name":"شريف عبد الله على سيد","client_amount":1683.635084906147,"customers":[]},{"client_id":5,"client_name":"مها حامد منصور محمد","client_amount":1903.2126235158703,"customers":[]},{"client_id":6,"client_name":"أ سعد منصور عبد السلام","client_amount":11983.943931188276,"customers":[]},{"client_id":8,"client_name":"نصره بشندي عطية عوض","client_amount":10768.85043263288,"customers":[]}]} 
[2025-07-17 22:58:17] local.INFO: Regular invoice clients {"clients":{"1":true,"2":true,"3":true,"4":true,"5":true,"6":true,"8":true}} 
[2025-07-17 22:58:17] local.INFO: Applying amounts to regular invoice {"reason_client_data_count":7,"regular_invoice_clients_count":7} 
[2025-07-17 22:58:17] local.INFO: Processing client {"client_id":1,"client_name":"کوثر یوسف احمد محمد","has_hours_in_regular":true,"client_amount":10008.838760818775,"customers_count":2} 
[2025-07-17 22:58:17] local.INFO: Processing client {"client_id":2,"client_name":"جوهرة سليمان عبد الكريم","has_hours_in_regular":true,"client_amount":14189.397283113554,"customers_count":0} 
[2025-07-17 22:58:17] local.INFO: Processing client {"client_id":3,"client_name":"تامر فاروق محمد سنوسي","has_hours_in_regular":true,"client_amount":13386.026664818497,"customers_count":0} 
[2025-07-17 22:58:17] local.INFO: Processing client {"client_id":4,"client_name":"شريف عبد الله على سيد","has_hours_in_regular":true,"client_amount":1683.635084906147,"customers_count":0} 
[2025-07-17 22:58:17] local.INFO: Processing client {"client_id":5,"client_name":"مها حامد منصور محمد","has_hours_in_regular":true,"client_amount":1903.2126235158703,"customers_count":0} 
[2025-07-17 22:58:17] local.INFO: Processing client {"client_id":6,"client_name":"أ سعد منصور عبد السلام","has_hours_in_regular":true,"client_amount":11983.943931188276,"customers_count":0} 
[2025-07-17 22:58:17] local.INFO: Processing client {"client_id":8,"client_name":"نصره بشندي عطية عوض","has_hours_in_regular":true,"client_amount":10768.85043263288,"customers_count":0} 
[2025-07-17 22:58:17] local.INFO: Finished applying amounts to regular invoice  
[2025-07-17 22:58:17] local.INFO: Successfully added amounts to regular invoice  
[2025-07-17 23:09:07] local.INFO: Starting addToRegularInvoice {"invoice_reason_id":"9","request_data":{"_token":"CrFTaOOTcBRQZlNUIgOIOW8gjPA4FUOce4qcQTC7","regular_invoice_id":"386"}} 
[2025-07-17 23:09:07] local.INFO: Found invoice reason {"invoice_reason":{"id":9,"client_id":null,"total_amount_due":"100000.00","reason":"تيست","start_date":"2024-12-31T22:00:00.000000Z","end_date":"2025-11-16T22:00:00.000000Z","wells_config":"[{\"id\":\"1\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0643\\u0628\\u064a\\u0631\",\"total_hours\":\"1307.9166666666667\",\"percentage\":\"34\",\"allocated_amount\":\"34000.00\"},{\"id\":\"2\",\"name\":\"\\u0627\\u0644\\u0628\\u0626\\u0631 \\u0627\\u0644\\u0635\\u063a\\u064a\\u0631\",\"total_hours\":\"202.25\",\"percentage\":\"66\",\"allocated_amount\":\"66000.00\"}]","wells_hourly_rates":"{\"1\":25.99553998088563,\"2\":326.3288009888752}","created_at":"2025-07-17T19:46:16.000000Z","updated_at":"2025-07-17T19:46:16.000000Z"}} 
[2025-07-17 23:09:07] local.INFO: Reason client data {"data":[{"client_id":1,"client_name":"کوثر یوسف احمد محمد","client_amount":10008.838760818775,"customers":[{"id":1,"name":"eslam","amount":24739.760271019204},{"id":2,"name":"hady","amount":11336.33494798679}]},{"client_id":2,"client_name":"جوهرة سليمان عبد الكريم","client_amount":14189.397283113554,"customers":[]},{"client_id":3,"client_name":"تامر فاروق محمد سنوسي","client_amount":13386.026664818497,"customers":[]},{"client_id":4,"client_name":"شريف عبد الله على سيد","client_amount":1683.635084906147,"customers":[]},{"client_id":5,"client_name":"مها حامد منصور محمد","client_amount":1903.2126235158703,"customers":[]},{"client_id":6,"client_name":"أ سعد منصور عبد السلام","client_amount":11983.943931188276,"customers":[]},{"client_id":8,"client_name":"نصره بشندي عطية عوض","client_amount":10768.85043263288,"customers":[]}]} 
[2025-07-17 23:09:07] local.INFO: Regular invoice clients {"clients":{"1":true,"2":true,"3":true,"4":true,"5":true,"6":true,"8":true}} 
[2025-07-17 23:09:07] local.INFO: Applying amounts to regular invoice {"reason_client_data_count":7,"regular_invoice_clients_count":7} 
[2025-07-17 23:09:07] local.INFO: Processing client {"client_id":1,"client_name":"کوثر یوسف احمد محمد","has_hours_in_regular":true,"client_amount":10008.838760818775,"customers_count":2} 
[2025-07-17 23:09:07] local.INFO: Updated client payment {"client_id":1,"invoice_id":"386","additional_amount_added":46084.93397982477,"new_additional_paid":46084.93397982477,"new_remaining_amount":60235.64397982477} 
[2025-07-17 23:09:07] local.INFO: Processing client {"client_id":2,"client_name":"جوهرة سليمان عبد الكريم","has_hours_in_regular":true,"client_amount":14189.397283113554,"customers_count":0} 
[2025-07-17 23:09:07] local.INFO: Updated client payment {"client_id":2,"invoice_id":"386","additional_amount_added":14189.397283113554,"new_additional_paid":14189.397283113554,"new_remaining_amount":15995.407283113555} 
[2025-07-17 23:09:07] local.INFO: Processing client {"client_id":3,"client_name":"تامر فاروق محمد سنوسي","has_hours_in_regular":true,"client_amount":13386.026664818497,"customers_count":0} 
[2025-07-17 23:09:07] local.INFO: Updated client payment {"client_id":3,"invoice_id":"386","additional_amount_added":13386.026664818497,"new_additional_paid":13386.026664818497,"new_remaining_amount":24236.0266648185} 
[2025-07-17 23:09:07] local.INFO: Processing client {"client_id":4,"client_name":"شريف عبد الله على سيد","has_hours_in_regular":true,"client_amount":1683.635084906147,"customers_count":0} 
[2025-07-17 23:09:07] local.INFO: Updated client payment {"client_id":4,"invoice_id":"386","additional_amount_added":1683.635084906147,"new_additional_paid":1683.635084906147,"new_remaining_amount":1683.635084906147} 
[2025-07-17 23:09:07] local.INFO: Processing client {"client_id":5,"client_name":"مها حامد منصور محمد","has_hours_in_regular":true,"client_amount":1903.2126235158703,"customers_count":0} 
[2025-07-17 23:09:07] local.INFO: Updated client payment {"client_id":5,"invoice_id":"386","additional_amount_added":1903.2126235158703,"new_additional_paid":1903.2126235158703,"new_remaining_amount":2810.1726235158703} 
[2025-07-17 23:09:07] local.INFO: Processing client {"client_id":6,"client_name":"أ سعد منصور عبد السلام","has_hours_in_regular":true,"client_amount":11983.943931188276,"customers_count":0} 
[2025-07-17 23:09:07] local.INFO: Updated client payment {"client_id":6,"invoice_id":"386","additional_amount_added":11983.943931188276,"new_additional_paid":11983.943931188276,"new_remaining_amount":28832.663931188275} 
[2025-07-17 23:09:07] local.INFO: Processing client {"client_id":8,"client_name":"نصره بشندي عطية عوض","has_hours_in_regular":true,"client_amount":10768.85043263288,"customers_count":0} 
[2025-07-17 23:09:07] local.INFO: Updated client payment {"client_id":8,"invoice_id":"386","additional_amount_added":10768.85043263288,"new_additional_paid":10768.85043263288,"new_remaining_amount":13741.01043263288} 
[2025-07-17 23:09:07] local.INFO: Finished applying amounts to regular invoice  
[2025-07-17 23:09:07] local.INFO: Successfully added amounts to regular invoice  
