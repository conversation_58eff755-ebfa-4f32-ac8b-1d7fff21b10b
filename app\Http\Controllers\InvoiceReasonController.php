<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\InvoiceReason;
use App\Models\ClientPaymentReason;
use App\Models\ClientPaymentReasonHistory;
use App\Notifications\PaymentStatusReasonUpdatedNotification;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class InvoiceReasonController extends Controller
{

    public function index(Request $request)
{
    // Get the search input from the request
    $search = $request->input('search');

    // Build the base query for invoices with client payment details
    $invoicesQuery = InvoiceReason::with(['clientPayments' => function($query) {
        $query->select('invoice_id', 'client_id', 'amount_due', 'payment_status')
              ->with('client:id,name'); // Ensure to adjust as necessary for your Client model
    }]);

    // If a search term is provided, filter the results
    if ($search) {
        $invoicesQuery->where(function($query) use ($search) {
            $query->where('reason', 'like', '%' . $search . '%')
                  ->orWhereHas('clientPayments.client', function($query) use ($search) {
                      $query->where('name', 'like', '%' . $search . '%');
                  });
        });
    }

    // Get the paginated results
    $invoices = $invoicesQuery->paginate(10)->appends(['search' => $search]);

    // Handle AJAX request for search results
    if ($request->ajax()) {
        return view('invoice-reason.invoice-list', compact('invoices'))->render();
    }

    return view('invoice-reason.index', compact('invoices'));
}


    public function create()
    {
        // Show the form to create an invoice
        return view('invoice-reason.create');
    }

    public function getWellsData(Request $request)
    {
        try {
            $request->validate([
                'start_date' => 'required|date',
                'end_date' => 'required|date|after_or_equal:start_date',
            ]);

            $startDate = Carbon::parse($request->input('start_date'));
            $endDate = Carbon::parse($request->input('end_date'));

            Log::info('Getting wells data for period: ' . $startDate . ' to ' . $endDate);

        // جلب جميع الآبار مع الساعات المعمولة في الفترة المحددة
        $wellsData = [];

        // جلب جميع العملاء مع ساعاتهم وساعات العملاء المشاركين
        $clients = Client::with([
            'hours' => function($query) use ($startDate, $endDate) {
                $query->whereBetween('start_time', [$startDate, $endDate])
                      ->whereNull('deleted_at')
                      ->with('well');
            },
            'customers.hours' => function($query) use ($startDate, $endDate) {
                $query->whereBetween('start_time', [$startDate, $endDate])
                      ->whereNull('deleted_at')
                      ->with('well');
            }
        ])->get();

        foreach ($clients as $client) {
            // معالجة ساعات العميل الأساسي
            foreach ($client->hours as $hour) {
                $wellId = $hour->well_id;
                $wellName = $hour->well->name ?? 'بئر غير معروف';

                $start = Carbon::parse($hour->start_time);
                $end = Carbon::parse($hour->end_time);
                $hoursWorked = $start->floatDiffInHours($end);

                if (!isset($wellsData[$wellId])) {
                    $wellsData[$wellId] = [
                        'id' => $wellId,
                        'name' => $wellName,
                        'total_hours' => 0
                    ];
                }

                $wellsData[$wellId]['total_hours'] += $hoursWorked;
            }

            // معالجة ساعات العملاء المشاركين
            foreach ($client->customers as $customer) {
                foreach ($customer->hours as $hour) {
                    $wellId = $hour->well_id;
                    $wellName = $hour->well->name ?? 'بئر غير معروف';

                    $start = Carbon::parse($hour->start_time);
                    $end = Carbon::parse($hour->end_time);
                    $hoursWorked = $start->floatDiffInHours($end);

                    if (!isset($wellsData[$wellId])) {
                        $wellsData[$wellId] = [
                            'id' => $wellId,
                            'name' => $wellName,
                            'total_hours' => 0
                        ];
                    }

                    $wellsData[$wellId]['total_hours'] += $hoursWorked;
                }
            }
        }

        // تصفية الآبار التي لها ساعات فقط
        $wellsData = array_filter($wellsData, function($well) {
            return $well['total_hours'] > 0;
        });

        // إعادة ترتيب المصفوفة
        $wellsData = array_values($wellsData);

            Log::info('Wells data found: ' . count($wellsData) . ' wells');
            Log::info('Wells data: ' . json_encode($wellsData));

            return response()->json([
                'success' => true,
                'wells' => $wellsData,
                'total_wells' => count($wellsData),
                'total_hours' => array_sum(array_column($wellsData, 'total_hours'))
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب البيانات: ' . $e->getMessage()
            ], 500);
        }
    }

    public function store(Request $request)
{
    // Validate the form input
    $request->validate([
        'total_amount_due' => 'required|numeric',
        'reason' => 'required|string',
        'start_date' => 'required|date',
        'end_date' => 'required|date|after_or_equal:start_date',
    ]);

    // التحقق من وجود بيانات الآبار (النظام الجديد)
    if ($request->has('wells') && is_array($request->wells)) {
        // النظام الجديد - التحقق من صحة بيانات الآبار
        $request->validate([
            'wells' => 'required|array',
            'wells.*.id' => 'required|integer',
            'wells.*.percentage' => 'required|numeric|min:0|max:100',
            'wells.*.allocated_amount' => 'required|numeric|min:0',
        ]);

        // التحقق من أن مجموع النسب = 100%
        $totalPercentage = array_sum(array_column($request->wells, 'percentage'));
        if (abs($totalPercentage - 100) > 0.01) {
            return back()->withErrors(['wells' => 'مجموع النسب يجب أن يساوي 100%'])->withInput();
        }

        $useNewSystem = true;
        $wellsConfig = $request->input('wells');
    } else {
        // النظام القديم - استخدام سعر ساعة واحد للكل
        $useNewSystem = false;
        $wellsConfig = [];
    }

    $totalAmountDue = $request->input('total_amount_due');
    $reason = $request->input('reason');
    $startDate = Carbon::parse($request->input('start_date'));
    $endDate = Carbon::parse($request->input('end_date'));

    // حساب سعر الساعة لكل بئر بناءً على النسب (النظام الجديد فقط)
    $wellHourlyRates = [];
    if ($useNewSystem) {
        foreach ($wellsConfig as $wellConfig) {
            $wellId = $wellConfig['id'];
            $totalHours = $wellConfig['total_hours'];
            $allocatedAmount = $wellConfig['allocated_amount'];

            if ($totalHours > 0) {
                $wellHourlyRates[$wellId] = $allocatedAmount / $totalHours;
            }
        }
    }

    // Get all clients with hours worked during the period (including customer hours)
    $clients = Client::with([
        'hours' => function($query) use ($startDate, $endDate) {
            $query->whereBetween('start_time', [$startDate, $endDate])
                  ->whereNull('deleted_at')
                  ->with('well');
        },
        'customers.hours' => function($query) use ($startDate, $endDate) {
            $query->whereBetween('start_time', [$startDate, $endDate])
                  ->whereNull('deleted_at')
                  ->with('well');
        },
        'customers'
    ])->get();

    // Collect client data for invoice display
    $clientData = [];
    $totalHoursWorked = 0;

    foreach ($clients as $client) {
        if ($useNewSystem) {
            // النظام الجديد - حساب ساعات العميل الأساسي حسب البئر
            $clientHoursByWell = [];
            $clientAmountDue = 0;

            foreach ($client->hours as $hour) {
                $wellId = $hour->well_id;
                $start = Carbon::parse($hour->start_time);
                $end = Carbon::parse($hour->end_time);
                $hoursWorked = $start->floatDiffInHours($end);

                if (!isset($clientHoursByWell[$wellId])) {
                    $clientHoursByWell[$wellId] = 0;
                }
                $clientHoursByWell[$wellId] += $hoursWorked;

                // حساب المبلغ المستحق بناءً على سعر ساعة البئر
                if (isset($wellHourlyRates[$wellId])) {
                    $clientAmountDue += $hoursWorked * $wellHourlyRates[$wellId];
                }
            }
        } else {
            // النظام القديم - حساب الساعات العادي
            $clientHoursWorked = $client->hours->sum(function ($hour) {
                $start = Carbon::parse($hour->start_time);
                $end = Carbon::parse($hour->end_time);
                return $start->floatDiffInHours($end);
            });
            $clientHoursByWell = ['total' => $clientHoursWorked];
            $clientAmountDue = 0; // سيتم حسابه لاحقاً
        }

        // حساب ساعات العملاء المشاركين
        $customerDetails = [];
        $customerAmountDue = 0;

        foreach ($client->customers as $customer) {
            if ($useNewSystem) {
                // النظام الجديد - حساب حسب البئر
                $customerHoursByWell = [];
                $customerAmount = 0;

                foreach ($customer->hours as $hour) {
                    $wellId = $hour->well_id;
                    $start = Carbon::parse($hour->start_time);
                    $end = Carbon::parse($hour->end_time);
                    $hoursWorked = $start->floatDiffInHours($end);

                    if (!isset($customerHoursByWell[$wellId])) {
                        $customerHoursByWell[$wellId] = 0;
                    }
                    $customerHoursByWell[$wellId] += $hoursWorked;

                    // حساب المبلغ المستحق بناءً على سعر ساعة البئر
                    if (isset($wellHourlyRates[$wellId])) {
                        $customerAmount += $hoursWorked * $wellHourlyRates[$wellId];
                    }
                }

                if ($customerAmount > 0) {
                    $customerAmountDue += $customerAmount;
                    $customerDetails[] = [
                        'name' => $customer->name,
                        'hours' => array_sum($customerHoursByWell),
                        'amount' => $customerAmount,
                        'wells_hours' => $customerHoursByWell
                    ];
                }
            } else {
                // النظام القديم - حساب الساعات العادي
                $customerHours = $customer->hours->sum(function ($hour) {
                    $start = Carbon::parse($hour->start_time);
                    $end = Carbon::parse($hour->end_time);
                    return $start->floatDiffInHours($end);
                });

                if ($customerHours > 0) {
                    $customerDetails[] = [
                        'name' => $customer->name,
                        'hours' => $customerHours,
                        'amount' => 0 // سيتم حسابه لاحقاً
                    ];
                }
            }
        }

        if ($useNewSystem) {
            $totalClientAmount = $clientAmountDue + $customerAmountDue;
            $totalClientHours = array_sum($clientHoursByWell) + array_sum(array_column($customerDetails, 'hours'));
        } else {
            // النظام القديم - حساب الساعات الإجمالية
            $totalClientHours = $clientHoursByWell['total'] + array_sum(array_column($customerDetails, 'hours'));
            $totalClientAmount = 0; // سيتم حسابه بعد معرفة سعر الساعة
        }

        // Only add to clientData if hours were worked
        if ($totalClientHours > 0) {
            $totalHoursWorked += $totalClientHours;
            $clientData[] = [
                'client_id' => $client->id,
                'client' => $client->name,
                'client_hours' => $useNewSystem ? array_sum($clientHoursByWell) : $clientHoursByWell['total'],
                'client_amount' => $clientAmountDue,
                'customer_hours' => array_sum(array_column($customerDetails, 'hours')),
                'customer_amount' => $customerAmountDue,
                'total_hours' => $totalClientHours,
                'total_amount' => $totalClientAmount,
                'customer_details' => $customerDetails,
                'client_wells_hours' => $clientHoursByWell,
                'use_new_system' => $useNewSystem
            ];
        }
    }

    // Check if there are any worked hours
    if ($totalHoursWorked > 0) {
        // Create the invoice with wells configuration
        $invoice = InvoiceReason::create([
            'total_amount_due' => $totalAmountDue,
            'reason' => $reason,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'wells_config' => $useNewSystem ? json_encode($wellsConfig) : null,
            'wells_hourly_rates' => $useNewSystem ? json_encode($wellHourlyRates) : null,
        ]);

        if (!$useNewSystem) {
            // النظام القديم - حساب سعر الساعة الموحد
            $hourlyRate = $totalAmountDue / $totalHoursWorked;

            // إعادة حساب المبالغ للعملاء
            foreach ($clientData as &$data) {
                $data['client_amount'] = $data['client_hours'] * $hourlyRate;
                $data['customer_amount'] = $data['customer_hours'] * $hourlyRate;
                $data['total_amount'] = $data['client_amount'] + $data['customer_amount'];

                // إعادة حساب مبالغ العملاء المشاركين
                foreach ($data['customer_details'] as &$customer) {
                    $customer['amount'] = $customer['hours'] * $hourlyRate;
                }
            }
        }

        // إنشاء سجلات الدفع للعملاء
        foreach ($clientData as $data) {
            if ($data['total_amount'] > 0) {
                ClientPaymentReason::create([
                    'invoice_id' => $invoice->id,
                    'client_id' => $data['client_id'],
                    'amount_due' => $data['total_amount'],
                    'hours_worked' => $data['total_hours'],
                    'payment_status' => 'unpaid',
                    'remaining_amount' => $data['total_amount'],
                ]);
            }
        }

        // Redirect to a dedicated invoice detail page with client data and invoice info
        return redirect()->route('invoice-reason.show', ['id' => $invoice->id])
            ->with('success', 'Invoice created and amounts calculated for all clients.');
    }

    return redirect()->back()->with('error', 'No hours worked in the selected period.');
}

// Update the payment status for the invoice reason
public function updatePaymentStatusReason(Request $request)
{
    $validated = $request->validate([
        'client_ids' => 'required|array',
        'client_ids.*' => 'exists:clients,id',
        'period' => 'required|string',
        'payment_status' => 'required|in:unpaid,partial,paid',
        'partial_payment' => 'nullable|numeric|min:0',
        'payment_date' => 'required|date',
    ]);

    $clientIds = $validated['client_ids'];
    $selectedPeriod = $validated['period'];

    // Split the selected period into start and end dates
    [$startDateString, $endDateString] = explode(' to ', $selectedPeriod);
    $startDate = Carbon::parse($startDateString)->startOfDay();
    $endDate = Carbon::parse($endDateString)->endOfDay();

    // Get the invoice reason (period) corresponding to the selected period
    $invoiceReason = InvoiceReason::whereDate('start_date', $startDate)
        ->whereDate('end_date', $endDate)
        ->first();

    if (!$invoiceReason) {
        return redirect()->back()->withErrors('Selected period is invalid.');
    }

    foreach ($clientIds as $clientId) {
        // Get the client payment reasons for the selected period
        $clientPaymentReasons = ClientPaymentReason::with('invoiceReason') // Eager-load invoiceReason relationship
            ->where('client_id', $clientId)
            ->where('invoice_id', $invoiceReason->id) // Use the period to filter
            ->get();


        if ($clientPaymentReasons->isEmpty()) {
            continue;
        }

        foreach ($clientPaymentReasons as $clientPaymentReason) {
            $partialPayment = $validated['partial_payment'] ?? 0;
            $currentPartialPayment = $clientPaymentReason->partial_payment ?? 0;
            $newPartialPayment = $currentPartialPayment + $partialPayment;
            $remainingAmount = $clientPaymentReason->amount_due - $newPartialPayment;
            // dd([
            //     'clientPaymentReason' => $clientPaymentReason,
            //     'invoiceReason' => $clientPaymentReason->invoiceReason,
            //     'reason' => $reason,
            // ]);
            switch ($validated['payment_status']) {
                case 'paid':
                    $finalPaymentAmount = $clientPaymentReason->amount_due - $currentPartialPayment;
                    $clientPaymentReason->update([
                        'payment_status' => 'paid',
                        'partial_payment' => $clientPaymentReason->amount_due,
                        'remaining_amount' => 0,
                        'payment_date' => $validated['payment_date'],
                    ]);

                    ClientPaymentReasonHistory::create([
                        'client_payment_reason_id' => $clientPaymentReason->id,
                        'payment_amount' => $finalPaymentAmount,
                        'partial_payment' => $clientPaymentReason->amount_due,
                        'remaining_amount' => 0,
                        'payment_date' => $validated['payment_date'],
                    ]);
                    break;

                case 'partial':
                    $clientPaymentReason->update([
                        'payment_status' => 'partial',
                        'partial_payment' => $newPartialPayment,
                        'remaining_amount' => max($remainingAmount, 0),
                        'payment_date' => $validated['payment_date'],
                    ]);

                    ClientPaymentReasonHistory::create([
                        'client_payment_reason_id' => $clientPaymentReason->id,
                        'payment_amount' => $partialPayment,
                        'partial_payment' => $newPartialPayment,
                        'remaining_amount' => max($remainingAmount, 0),
                        'payment_date' => $validated['payment_date'],
                    ]);
                    break;

                case 'unpaid':
                    $clientPaymentReason->update([
                        'payment_status' => 'unpaid',
                        'partial_payment' => null,
                        'remaining_amount' => $clientPaymentReason->amount_due,
                        'payment_date' => $validated['payment_date'],
                    ]);
                    break;
            }
        }
        // foreach ($clientPaymentReasons as $clientPaymentReason) {
        //     // Your existing code...

        //     // Dump relevant data before notifying
        //     $messageData = [
        //         'reason' => $reason, // Ensure this exists
        //         'period' => $selectedPeriod,
        //         'status' => $validated['payment_status'],
        //         'partial_payment' => $validated['partial_payment'],
        //         'payment_date' => $validated['payment_date'],
        //     ];

        //     dd($messageData); // This will stop execution and dump the contents
        // }
        $client = Client::find($clientId);
        // Here we assume that the last processed payment reason is what we want to notify
        $latestPaymentReason = $clientPaymentReasons->last();
        $client->notify(new PaymentStatusReasonUpdatedNotification($latestPaymentReason, $selectedPeriod, $validated['payment_status'], $validated['partial_payment'], $validated['payment_date']));
    }

    return redirect()->back()->with('success', 'Payment status updated successfully.');
}




public function getAmountDue(Request $request)
{
    $period = $request->input('period');
    $clientIds = $request->input('client_ids', []);

    [$startDateString, $endDateString] = explode(' to ', $period);
    $startDate = Carbon::parse($startDateString)->startOfDay();
    $endDate = Carbon::parse($endDateString)->endOfDay();

    // Get the invoice reason (period) corresponding to the selected period
    $invoiceReason = InvoiceReason::whereDate('start_date', $startDate)
        ->whereDate('end_date', $endDate)
        ->first();

    if (!$invoiceReason) {
        return response()->json(['amount_due' => 0]);
    }

    // Query ClientPaymentReason records linked to this period
    $amountDue = ClientPaymentReason::whereIn('client_id', $clientIds)
        ->where('invoice_id', $invoiceReason->id)
        ->sum('remaining_amount');

    return response()->json(['amount_due' => $amountDue]);
}









public function showUpdatePaymentForm(Request $request)
{
    // Fetch all invoice reasons, or scope this to specific clients if needed
    $invoiceReasons = InvoiceReason::get(['start_date', 'end_date', 'reason', 'id']);

    // Format the time periods for the dropdown
    $timePeriods = $invoiceReasons->map(function ($invoiceReason) {
        return Carbon::parse($invoiceReason->start_date)->format('Y-m-d') . ' to ' . Carbon::parse($invoiceReason->end_date)->format('Y-m-d');
    });

    // Get the selected time period from the request or set the default to the first available period
    $selectedPeriod = $request->query('period', $timePeriods->first());
    [$startDate, $endDate] = explode(' to ', $selectedPeriod);

    // Convert to Carbon dates for filtering
    $startDate = Carbon::parse($startDate)->startOfDay();
    $endDate = Carbon::parse($endDate)->endOfDay();

    // Fetch clients with unpaid or partial invoices within the selected time period
    $clients = Client::whereHas('clientPaymentReasons', function ($query) use ($startDate, $endDate) {
        $query->where(function ($q) {
            $q->where('payment_status', 'unpaid')
              ->orWhere('payment_status', 'partial');
        })
        ->whereBetween('created_at', [$startDate, $endDate]);
    })
    ->with(['clientPaymentReasons' => function ($query) use ($startDate, $endDate) {
        $query->where(function ($q) {
            $q->where('payment_status', 'unpaid')
              ->orWhere('payment_status', 'partial');
        })
        ->whereBetween('created_at', [$startDate, $endDate]);
    }])
    ->get();


    return view('invoice-reason.update-payment-status', compact('clients', 'startDate', 'endDate', 'timePeriods', 'selectedPeriod'));
}


public function showPartialPaymentReason($clientId)
{
    // Retrieve the client information
    $client = Client::findOrFail($clientId);

    // Get all payment reasons and associated payment histories for this client
    $partialPayments = ClientPaymentReason::where('client_id', $clientId)
        ->with('paymentHistories') // Load the payment histories for each invoice
        ->with('invoiceReason') // Load invoice details
        ->get();

    // Return the view with the client and partial payment data
    return view('partial-payments-reason.index', compact('client', 'partialPayments'));
}








public function edit($id)
{
    // Find the invoice by ID
    $invoice = InvoiceReason::findOrFail($id);

    // Pass the invoice data to the view
    return view('invoice-reason.edit', compact('invoice'));
}

public function update(Request $request, $id)
{
    // Validate the form input
    $request->validate([
        'total_amount_due' => 'required|numeric',
        'reason' => 'required|string',
        'start_date' => 'required|date',
        'end_date' => 'required|date|after_or_equal:start_date',
    ]);

    // Find the invoice by ID
    $invoice = InvoiceReason::findOrFail($id);

    // Retrieve new input values
    $totalAmountDue = $request->input('total_amount_due');
    $reason = $request->input('reason');
    $startDate = Carbon::parse($request->input('start_date'));
    $endDate = Carbon::parse($request->input('end_date'));

    // Get all clients with hours worked during the period (including customer hours)
    $clients = Client::with([
        'hours' => function($query) use ($startDate, $endDate) {
            $query->whereBetween('start_time', [$startDate, $endDate])
                  ->whereNull('deleted_at');
        },
        'customers.hours' => function($query) use ($startDate, $endDate) {
            $query->whereBetween('start_time', [$startDate, $endDate])
                  ->whereNull('deleted_at');
        },
        'customers' // تحميل معلومات العملاء المشاركين
    ])->get();

    // Calculate total hours worked by all clients and their customers in the selected period
    $totalHoursWorked = 0;

    foreach ($clients as $client) {
        // Calculate client's direct hours
        $clientHoursWorked = $client->hours->sum(function ($hour) {
            $start = Carbon::parse($hour->start_time);
            $end = Carbon::parse($hour->end_time);
            return $start->floatDiffInHours($end);
        });

        // Calculate customer hours for this client
        $customerHoursWorked = 0;
        foreach ($client->customers as $customer) {
            $customerHours = $customer->hours->sum(function ($hour) {
                $start = Carbon::parse($hour->start_time);
                $end = Carbon::parse($hour->end_time);
                return $start->floatDiffInHours($end);
            });
            $customerHoursWorked += $customerHours;
        }

        $totalHoursWorked += ($clientHoursWorked + $customerHoursWorked);
    }

    // Check if there are any worked hours
    if ($totalHoursWorked > 0) {
        // Calculate the hourly rate
        $hourlyRate = $totalAmountDue / $totalHoursWorked;

        // Update the invoice with new data
        $invoice->update([
            'total_amount_due' => $totalAmountDue,
            'reason' => $reason,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);

        // Recalculate amount due for each client and update ClientPaymentReason
        foreach ($clients as $client) {
            // Calculate client's direct hours
            $clientHoursWorked = $client->hours->sum(function ($hour) {
                $start = Carbon::parse($hour->start_time);
                $end = Carbon::parse($hour->end_time);
                return $start->floatDiffInHours($end);
            });

            // Calculate customer hours for this client
            $customerHoursWorked = 0;
            foreach ($client->customers as $customer) {
                $customerHours = $customer->hours->sum(function ($hour) {
                    $start = Carbon::parse($hour->start_time);
                    $end = Carbon::parse($hour->end_time);
                    return $start->floatDiffInHours($end);
                });
                $customerHoursWorked += $customerHours;
            }

            $totalClientHours = $clientHoursWorked + $customerHoursWorked;

            if ($totalClientHours > 0) {
                // Calculate the updated amount due for this client (including customer hours)
                $clientAmountDue = $totalClientHours * $hourlyRate;

                // Update the client payment record
                ClientPaymentReason::where('invoice_id', $invoice->id)
                    ->where('client_id', $client->id)
                    ->update([
                        'amount_due' => $clientAmountDue,
                        'hours_worked' => $totalClientHours,
                        'remaining_amount' => $clientAmountDue,
                    ]);
            }
        }

        // Redirect to the invoice detail page with success message
        return redirect()->route('invoice-reason.show', ['id' => $invoice->id])
            ->with('success', 'Invoice updated successfully and amounts recalculated for all clients.');
    }

    return redirect()->back()->with('error', 'No hours worked in the selected period.');
}



public function show($id)
{
    // Retrieve the invoice by ID
    $invoice = InvoiceReason::findOrFail($id);
    $invoice->start_date = Carbon::parse($invoice->start_date);
    $invoice->end_date = Carbon::parse($invoice->end_date);

    // جلب إعدادات الآبار وأسعار الساعات
    $wellsConfig = $invoice->wells_config ?? [];
    $wellHourlyRates = $invoice->wells_hourly_rates ?? [];

    // Fetch clients with their payment records (including customer hours)
    $clients = Client::with([
        'hours' => function($query) use ($invoice) {
            $query->whereBetween('start_time', [$invoice->start_date, $invoice->end_date])
                  ->whereNull('deleted_at')
                  ->with('well');
        },
        'customers.hours' => function($query) use ($invoice) {
            $query->whereBetween('start_time', [$invoice->start_date, $invoice->end_date])
                  ->whereNull('deleted_at')
                  ->with('well');
        },
        'customers'
    ])->get();

    $totalHoursWorked = 0;
    $clientData = [];

    foreach ($clients as $client) {
        // حساب ساعات العميل الأساسي حسب البئر
        $clientHoursByWell = [];
        $clientAmountDue = 0;

        foreach ($client->hours as $hour) {
            $wellId = $hour->well_id;
            $wellName = $hour->well->name ?? 'بئر غير معروف';
            $start = Carbon::parse($hour->start_time);
            $end = Carbon::parse($hour->end_time);
            $hoursWorked = $start->floatDiffInHours($end);

            if (!isset($clientHoursByWell[$wellId])) {
                $clientHoursByWell[$wellId] = [
                    'name' => $wellName,
                    'hours' => 0,
                    'hourly_rate' => $wellHourlyRates[$wellId] ?? 0
                ];
            }
            $clientHoursByWell[$wellId]['hours'] += $hoursWorked;

            // حساب المبلغ المستحق بناءً على سعر ساعة البئر
            if (isset($wellHourlyRates[$wellId])) {
                $clientAmountDue += $hoursWorked * $wellHourlyRates[$wellId];
            }
        }

        // حساب ساعات العملاء المشاركين حسب البئر
        $customerDetails = [];
        $customerAmountDue = 0;

        foreach ($client->customers as $customer) {
            $customerHoursByWell = [];
            $customerAmount = 0;

            foreach ($customer->hours as $hour) {
                $wellId = $hour->well_id;
                $wellName = $hour->well->name ?? 'بئر غير معروف';
                $start = Carbon::parse($hour->start_time);
                $end = Carbon::parse($hour->end_time);
                $hoursWorked = $start->floatDiffInHours($end);

                if (!isset($customerHoursByWell[$wellId])) {
                    $customerHoursByWell[$wellId] = [
                        'name' => $wellName,
                        'hours' => 0,
                        'hourly_rate' => $wellHourlyRates[$wellId] ?? 0
                    ];
                }
                $customerHoursByWell[$wellId]['hours'] += $hoursWorked;

                // حساب المبلغ المستحق بناءً على سعر ساعة البئر
                if (isset($wellHourlyRates[$wellId])) {
                    $customerAmount += $hoursWorked * $wellHourlyRates[$wellId];
                }
            }

            if ($customerAmount > 0) {
                $customerAmountDue += $customerAmount;
                $customerDetails[] = [
                    'name' => $customer->name,
                    'hours' => array_sum(array_column($customerHoursByWell, 'hours')),
                    'amount' => $customerAmount,
                    'wells' => $customerHoursByWell
                ];
            }
        }

        $totalClientAmount = $clientAmountDue + $customerAmountDue;
        $totalClientHours = array_sum(array_column($clientHoursByWell, 'hours')) + array_sum(array_column($customerDetails, 'hours'));

        // Only add to clientData if hours were worked
        if ($totalClientHours > 0) {
            $totalHoursWorked += $totalClientHours;

            // جلب معلومات الدفع
            $paymentInfo = ClientPaymentReason::where('invoice_id', $invoice->id)
                ->where('client_id', $client->id)
                ->first();

            $clientData[] = [
                'client_id' => $client->id,
                'client' => $client->name,
                'client_hours' => array_sum(array_column($clientHoursByWell, 'hours')),
                'client_amount' => $clientAmountDue,
                'customer_hours' => array_sum(array_column($customerDetails, 'hours')),
                'customer_amount' => $customerAmountDue,
                'total_hours' => $totalClientHours,
                'total_amount' => $totalClientAmount,
                'customer_details' => $customerDetails,
                'client_wells' => $clientHoursByWell,
                'payment_status' => $paymentInfo->payment_status ?? 'unpaid',
                'remaining_amount' => $paymentInfo->remaining_amount ?? $totalClientAmount
            ];
        }
    }

    // إضافة معلومات الآبار وأسعار الساعات للعرض
    $wellsInfo = [];
    foreach ($wellsConfig as $wellConfig) {
        $wellId = $wellConfig['id'];
        $wellsInfo[$wellId] = [
            'name' => $wellConfig['name'],
            'total_hours' => $wellConfig['total_hours'],
            'percentage' => $wellConfig['percentage'],
            'allocated_amount' => $wellConfig['allocated_amount'],
            'hourly_rate' => $wellHourlyRates[$wellId] ?? 0
        ];
    }

    // Pass the data to the view
    return view('invoice-reason.show', compact('invoice', 'clientData', 'totalHoursWorked', 'wellsInfo'));
}

public function destroy($id)
{
    // Find the invoice by ID
    $invoice = InvoiceReason::findOrFail($id);

    // Delete the associated client payments
    ClientPaymentReason::where('invoice_id', $invoice->id)->delete();

    // Delete the invoice
    $invoice->delete();

    // Redirect to the invoice list with a success message
    return redirect()->route('invoice-reason.index')
        ->with('success', 'Invoice deleted successfully.');
}



}

