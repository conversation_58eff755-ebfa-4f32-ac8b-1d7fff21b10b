<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientPaymentReason extends Model
{
    use HasFactory;

    protected $fillable = ['invoice_id', 'client_id', 'customer_id', 'amount_due', 'payment_status','partial_payment', 'remaining_amount', 'hours_worked', 'payment_date'];

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function invoiceReason()
    {
        return $this->belongsTo(InvoiceReason::class, 'invoice_id');
    }
    public function paymentHistories()
    {
        return $this->hasMany(ClientPaymentReasonHistory::class, 'client_payment_reason_id');
    }

}
